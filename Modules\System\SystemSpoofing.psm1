# System Spoofing Module
# BIOS, Drivers, and Services spoofing for VM detection bypass

# Import required modules
Import-Module "$PSScriptRoot\..\Core\Logging\Logging.psm1" -Force
Import-Module "$PSScriptRoot\..\Core\Utilities\Utilities.psm1" -Force
Import-Module "$PSScriptRoot\..\Registry\RegistryPrivileges\RegistryPrivileges.psm1" -Force

# BIOS Spoofing Functions
function Invoke-BIOSSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting BIOS spoofing..." "Info"
    
    try {
        $biosSpecs = $Config.HardwareSpecs.Motherboard
        
        # Spoof BIOS information
        $biosKeys = @(
            'HKLM:\HARDWARE\DESCRIPTION\System\BIOS',
            'HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation'
        )
        
        foreach ($keyPath in $biosKeys) {
            if (Test-Path $keyPath) {
                # Set BIOS identifiers
                Set-RegistryValue -Path $keyPath -Name "BIOSVendor" -Value $biosSpecs.BIOSVendor -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BIOSVersion" -Value $biosSpecs.BIOSVersion -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BIOSReleaseDate" -Value $biosSpecs.BIOSDate -Type "String"
                
                # Generate realistic BIOS serial
                $biosSerial = Get-RandomSerial -Manufacturer $biosSpecs.BIOSVendor
                Set-RegistryValue -Path $keyPath -Name "BIOSSerialNumber" -Value $biosSerial -Type "String"
                
                # Remove VM-specific BIOS signatures
                $vmBiosSignatures = @('VBOX', 'VMWARE', 'QEMU', 'BOCHS', 'SEABIOS')
                foreach ($signature in $vmBiosSignatures) {
                    try {
                        Remove-ItemProperty -Path $keyPath -Name $signature -ErrorAction SilentlyContinue
                    }
                    catch {
                        # Ignore errors for non-existent properties
                    }
                }
                
                Write-ModuleLog "Updated BIOS information in: $keyPath" "Debug"
            }
        }
        
        Write-ModuleLog "BIOS spoofing completed successfully" "Info"
        return @{ Success = $true; Message = "BIOS spoofed to $($biosSpecs.BIOSVendor) $($biosSpecs.BIOSVersion)" }
    }
    catch {
        Write-ModuleLog "BIOS spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# VMware Device Removal Functions
function Invoke-VMwareDeviceRemoval {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting VMware device removal..." "Info"
    
    try {
        $removedCount = 0
        
        # Remove VMware VMCI Bus Device from System Devices
        $systemClassPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}"
        if (Test-Path $systemClassPath) {
            Get-ChildItem -Path $systemClassPath -ErrorAction SilentlyContinue | ForEach-Object {
                # Convert to clean registry path
                $keyPath = $_.Name -replace '^.*?\\HKEY_LOCAL_MACHINE', 'HKLM:'
                $props = Get-ItemProperty -Path $keyPath -ErrorAction SilentlyContinue
                
                if ($props.DriverDesc -like "*VMware*" -or $props.DriverDesc -like "*VMCI*") {
                    Write-ModuleLog "Found VMware system device at: $keyPath" "Info"
                    
                    try {
                        # Take ownership if needed
                        Set-RegistryKeyOwnership -RegistryPath $keyPath | Out-Null
                        
                        # Remove the entire key
                        Remove-Item -Path $keyPath -Recurse -Force -ErrorAction Stop
                        Write-ModuleLog "Removed VMware system device key: $keyPath" "Info"
                        $removedCount++
                    } catch {
                        Write-ModuleLog "Failed to remove VMware device key: $_" "Warning"
                        
                        # If removal fails, try to at least modify the descriptions
                        try {
                            Set-ItemProperty -Path $keyPath -Name "DriverDesc" -Value "Intel(R) Management Engine Interface" -Force
                            Set-ItemProperty -Path $keyPath -Name "DeviceDesc" -Value "Intel(R) Management Engine Interface" -Force -ErrorAction SilentlyContinue
                            if ($props.PSObject.Properties['FriendlyName']) {
                                Set-ItemProperty -Path $keyPath -Name "FriendlyName" -Value "Intel(R) Management Engine Interface" -Force -ErrorAction SilentlyContinue
                            }
                            Write-ModuleLog "Modified VMware device descriptions at: $keyPath" "Info"
                            $removedCount++
                        } catch {
                            Write-ModuleLog "Failed to modify VMware device: $_" "Warning"
                        }
                    }
                }
            }
        }
        
        # Clean up VMware entries from device enumeration
        $enumPaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Enum\PCI",
            "HKLM:\SYSTEM\CurrentControlSet\Enum\ACPI",
            "HKLM:\SYSTEM\CurrentControlSet\Enum\Root"
        )
        
        foreach ($enumPath in $enumPaths) {
            if (Test-Path $enumPath) {
                Get-ChildItem $enumPath -Recurse -ErrorAction SilentlyContinue | Where-Object {
                    $_.Name -match "VMware|VMCI|VM3D|15AD"
                } | ForEach-Object {
                    try {
                        $path = $_.Name -replace '^.*?\\HKEY_LOCAL_MACHINE', 'HKLM:'
                        Set-RegistryKeyOwnership -RegistryPath $path | Out-Null
                        Remove-Item -Path $path -Recurse -Force -ErrorAction Stop
                        Write-ModuleLog "Removed VMware enum key: $path" "Debug"
                        $removedCount++
                    } catch {
                        Write-ModuleLog "Could not remove enum key ${path}: $_" "Debug"
                    }
                }
            }
        }
        
        # Remove VMware PnP devices
        try {
            $pnpDevices = Get-PnpDevice -Class System -Status OK,Error,Degraded,Unknown -ErrorAction SilentlyContinue | Where-Object { 
                $_.Name -like "*VMware*" -or 
                $_.Name -like "*VMCI*" -or 
                $_.FriendlyName -like "*VMware*" -or 
                $_.InstanceId -like "*VMware*"
            }
            
            foreach ($device in $pnpDevices) {
                try {
                    # Try to disable the device first
                    Disable-PnpDevice -InstanceId $device.InstanceId -Confirm:$false -ErrorAction Stop
                    Write-ModuleLog "Disabled VMware PnP device: $($device.Name)" "Info"
                    $removedCount++
                } catch {
                    Write-ModuleLog "Failed to disable PnP device $($device.Name): $_" "Debug"
                }
            }
        } catch {
            Write-ModuleLog "Failed to query PnP devices: $_" "Debug"
        }
        
        Write-ModuleLog "VMware device removal completed: $removedCount items processed" "Info"
        return @{ Success = $true; Message = "Removed/modified $removedCount VMware devices" }
    }
    catch {
        Write-ModuleLog "VMware device removal failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Driver Spoofing Functions
function Invoke-DriverSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting driver spoofing..." "Info"
    
    try {
        # First remove VMware devices
        $vmwareResult = Invoke-VMwareDeviceRemoval -Config $Config
        
        # Spoof driver information to remove VM signatures
        $driverKeys = @(
            'HKLM:\SYSTEM\CurrentControlSet\Services',
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class'
        )
        
        $vmDriverPatterns = @('vbox', 'vmware', 'qemu', 'virtual', 'hyper-v', 'vmci')
        $spoofedCount = 0
        
        foreach ($keyPath in $driverKeys) {
            if (Test-Path $keyPath) {
                $subKeys = Get-ChildItem -Path $keyPath -Recurse -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    # Convert to clean registry path
                    $currentPath = $subKey.Name -replace '^.*?\\HKEY_LOCAL_MACHINE', 'HKLM:'
                    
                    # Check for VM driver signatures in driver descriptions
                    try {
                        $driverDesc = Get-ItemProperty -Path $currentPath -Name "DriverDesc" -ErrorAction SilentlyContinue
                        if ($driverDesc) {
                            foreach ($pattern in $vmDriverPatterns) {
                                if ($driverDesc.DriverDesc -match $pattern) {
                                    # Replace with generic driver description
                                    $genericDesc = "Standard System Device"
                                    Set-RegistryValue -Path $currentPath -Name "DriverDesc" -Value $genericDesc -Type "String"
                                    Write-ModuleLog "Spoofed driver description: $currentPath" "Debug"
                                    $spoofedCount++
                                    break
                                }
                            }
                        }
                    }
                    catch {
                        # Continue processing other drivers
                    }
                }
            }
        }
        
        $totalProcessed = $spoofedCount
        if ($vmwareResult.Success) {
            $totalProcessed += [int]($vmwareResult.Message -match '(\d+)' -and $Matches[1])
        }
        
        Write-ModuleLog "Driver spoofing completed: $totalProcessed drivers/devices modified" "Info"
        return @{ Success = $true; Message = "Spoofed $totalProcessed VM drivers and devices" }
    }
    catch {
        Write-ModuleLog "Driver spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Service Management Functions
function Invoke-ServiceSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting service spoofing..." "Info"
    
    try {
        # VM-related services to disable/remove
        $vmServices = @(
            'VBoxGuest', 'VBoxMouse', 'VBoxService', 'VBoxSF', 'VBoxVideo',
            'vmci', 'vmhgfs', 'vmmouse', 'vmrawdsk', 'vmusbmouse', 'vmvss',
            'vmscsi', 'vmxnet', 'vmxnet3', 'VMTools', 'vm3dservice',
            'VGAuthService', 'VMUSBArbService'
        )
        
        $processedCount = 0
        foreach ($serviceName in $vmServices) {
            try {
                $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
                if ($service) {
                    # Stop the service if running
                    if ($service.Status -eq 'Running') {
                        Stop-Service -Name $serviceName -Force -ErrorAction SilentlyContinue
                        Write-ModuleLog "Stopped VM service: $serviceName" "Debug"
                    }
                    
                    # Disable the service
                    Set-Service -Name $serviceName -StartupType Disabled -ErrorAction SilentlyContinue
                    Write-ModuleLog "Disabled VM service: $serviceName" "Debug"
                    $processedCount++
                }
                
                # Remove service registry entries
                $serviceRegPath = "HKLM:\SYSTEM\CurrentControlSet\Services\$serviceName"
                if (Test-Path $serviceRegPath) {
                    Remove-Item -Path $serviceRegPath -Recurse -Force -ErrorAction SilentlyContinue
                    Write-ModuleLog "Removed service registry: $serviceRegPath" "Debug"
                }
            }
            catch {
                Write-ModuleLog "Failed to process service $serviceName`: $($_.Exception.Message)" "Warning"
            }
        }
        
        Write-ModuleLog "Service spoofing completed: $processedCount services processed" "Info"
        return @{ Success = $true; Message = "Processed $processedCount VM services" }
    }
    catch {
        Write-ModuleLog "Service spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Process Spoofing Functions
function Invoke-ProcessSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting process spoofing..." "Info"
    
    try {
        # VM-related processes to terminate
        $vmProcesses = @(
            'VBoxService', 'VBoxTray', 'vmtoolsd', 'vmware-vmx',
            'vmware-authd', 'vmacthlp', 'vm3dservice', 'VGAuthService'
        )
        
        $terminatedCount = 0
        foreach ($processName in $vmProcesses) {
            try {
                $processes = Get-Process -Name $processName -ErrorAction SilentlyContinue
                foreach ($process in $processes) {
                    $process.Kill()
                    Write-ModuleLog "Terminated VM process: $($process.Name) (PID: $($process.Id))" "Debug"
                    $terminatedCount++
                }
            }
            catch {
                Write-ModuleLog "Failed to terminate process $processName`: $($_.Exception.Message)" "Warning"
            }
        }
        
        Write-ModuleLog "Process spoofing completed: $terminatedCount processes terminated" "Info"
        return @{ Success = $true; Message = "Terminated $terminatedCount VM processes" }
    }
    catch {
        Write-ModuleLog "Process spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Main System Spoofing Function
function Invoke-SystemSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting comprehensive system spoofing..." "Info"
    $results = @()
    
    # Execute system spoofing modules
    if ($Config.Modules.System.BIOS.Enabled) {
        $results += Invoke-BIOSSpoofing -Config $Config
    }
    
    if ($Config.Modules.System.Drivers.Enabled) {
        $results += Invoke-DriverSpoofing -Config $Config
    }
    
    if ($Config.Modules.System.Services.Enabled) {
        $results += Invoke-ServiceSpoofing -Config $Config
    }
    
    if ($Config.Modules.System.Processes.Enabled) {
        $results += Invoke-ProcessSpoofing -Config $Config
    }
    
    $successCount = ($results | Where-Object { $_.Success }).Count
    $totalCount = $results.Count
    
    Write-ModuleLog "System spoofing completed: $successCount/$totalCount successful" "Info"
    
    return @{
        Success = $successCount -eq $totalCount
        Results = $results
        Summary = "System spoofing: $successCount/$totalCount modules successful"
    }
}

# Main orchestration function (called by main script)
function Invoke-System {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "=== System Module Execution Started ===" "Info"
    
    try {
        $results = @()
        $modifiedComponents = @()
        
        # Execute system operations based on configuration
        if ($Config.Modules.System.BIOS.Enabled) {
            $result = Invoke-BIOSSpoofing -Config $Config
            $results += $result
            if ($result.Success) {
                $modifiedComponents += "BIOS Spoofing"
            }
        }
        
        if ($Config.Modules.System.Drivers.Enabled) {
            $result = Invoke-DriverSpoofing -Config $Config
            $results += $result
            if ($result.Success) {
                $modifiedComponents += "Driver Spoofing"
            }
        }
        
        if ($Config.Modules.System.Services.Enabled) {
            $result = Invoke-ServiceSpoofing -Config $Config
            $results += $result
            if ($result.Success) {
                $modifiedComponents += "Service Management"
            }
        }
        
        if ($Config.Modules.System.Processes.Enabled) {
            $result = Invoke-ProcessSpoofing -Config $Config
            $results += $result
            if ($result.Success) {
                $modifiedComponents += "Process Management"
            }
        }
        
        $successCount = ($results | Where-Object { $_.Success }).Count
        $totalCount = $results.Count
        
        Write-ModuleLog "System module completed: $successCount/$totalCount operations successful" "Info"
        
        return @{
            Success = $successCount -gt 0
            ModifiedComponents = $modifiedComponents
            Summary = "System operations: $successCount/$totalCount successful"
            Details = $results
        }
    }
    catch {
        Write-ModuleLog "System module failed: $($_.Exception.Message)" "Error"
        return @{
            Success = $false
            ModifiedComponents = @()
            Summary = "System module failed: $($_.Exception.Message)"
        }
    }
}

# Export functions
Export-ModuleMember -Function @(
    'Invoke-System',
    'Invoke-SystemSpoofing',
    'Invoke-BIOSSpoofing',
    'Invoke-DriverSpoofing',
    'Invoke-VMwareDeviceRemoval',
    'Invoke-ServiceSpoofing',
    'Invoke-ProcessSpoofing'
)
