# System Information Spoofing Module
# Dynamically updates hardware specifications in System Properties and other Windows interfaces

# Import required modules
Import-Module "$PSScriptRoot\..\..\Core\Logging\Logging.psm1" -Force
Import-Module "$PSScriptRoot\..\..\Core\Utilities\Utilities.psm1" -Force
Import-Module "$PSScriptRoot\..\..\Registry\RegistryPrivileges\RegistryPrivileges.psm1" -Force

function Get-RandomRAMSize {
    <#
    .SYNOPSIS
        Generates a random but realistic RAM size between 4GB and 32GB
    #>
    [CmdletBinding()]
    param()
    
    # Common RAM sizes in GB
    $commonSizes = @(4, 8, 16, 32)
    $selectedSize = Get-Random -InputObject $commonSizes
    
    # Convert to bytes for registry storage
    $sizeInBytes = [long]$selectedSize * 1024 * 1024 * 1024
    
    return @{
        SizeGB = $selectedSize
        SizeBytes = $sizeInBytes
        SizeKB = $selectedSize * 1024 * 1024
        SizeMB = $selectedSize * 1024
    }
}

function Invoke-CPUCoreCountSpoofing {
    <#
    .SYNOPSIS
        Updates CPU core count to match spoofed processor specifications
    #>
    [CmdletBinding()]
    param(
        [hashtable]$CPUProfile
    )
    
    Write-ModuleLog "Updating CPU core count to match spoofed processor..." "Info"
    
    try {
        $totalModified = 0
        
        # Key registry locations that control CPU core count display
        $coreCountPaths = @(
            @{Path = "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Environment"; Name = "NUMBER_OF_PROCESSORS"},
            @{Path = "HKLM:\HARDWARE\DESCRIPTION\System"; Name = "NumberOfProcessors"},
            @{Path = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"; Name = "NumberOfProcessors"},
            @{Path = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"; Name = "NumberOfLogicalProcessors"}
        )
        
        foreach ($location in $coreCountPaths) {
            if (Test-Path $location.Path) {
                try {
                    if ($location.Name -eq "NumberOfLogicalProcessors") {
                        Set-ItemProperty -Path $location.Path -Name $location.Name -Value $CPUProfile.Threads -Force
                        Write-ModuleLog "Set logical processors to $($CPUProfile.Threads) at $($location.Path)" "Debug"
                    } else {
                        Set-ItemProperty -Path $location.Path -Name $location.Name -Value $CPUProfile.Cores -Force
                        Write-ModuleLog "Set core count to $($CPUProfile.Cores) at $($location.Path)" "Debug"
                    }
                    $totalModified++
                } catch {
                    Write-ModuleLog "Failed to update $($location.Name) at $($location.Path): $($_.Exception.Message)" "Warning"
                }
            }
        }
        
        # Update environment variables for immediate effect
        [Environment]::SetEnvironmentVariable("NUMBER_OF_PROCESSORS", $CPUProfile.Cores.ToString(), "Machine")
        [Environment]::SetEnvironmentVariable("NUMBER_OF_PROCESSORS", $CPUProfile.Cores.ToString(), "Process")
        
        # Create additional CPU core registry entries if needed
        for ($i = 0; $i -lt $CPUProfile.Cores; $i++) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$i"
            if (-not (Test-Path $cpuPath)) {
                New-Item -Path $cpuPath -Force | Out-Null
                Set-ItemProperty -Path $cpuPath -Name "ProcessorNameString" -Value $CPUProfile.Name -Force
                Set-ItemProperty -Path $cpuPath -Name "VendorIdentifier" -Value "GenuineIntel" -Force
                Set-ItemProperty -Path $cpuPath -Name "~MHz" -Value $CPUProfile.BaseFreq -Force
                $totalModified++
            }
        }
        
        Write-ModuleLog "CPU core count spoofing completed: $totalModified modifications" "Info"
        return @{ Success = $true; ModifiedCount = $totalModified; Message = "CPU cores updated to $($CPUProfile.Cores) cores, $($CPUProfile.Threads) threads" }
    }
    catch {
        Write-ModuleLog "CPU core count spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Invoke-RAMSpoofing {
    <#
    .SYNOPSIS
        Updates RAM size display with random realistic values
    #>
    [CmdletBinding()]
    param(
        [hashtable]$MemoryProfile = $null
    )
    
    Write-ModuleLog "Updating RAM size display..." "Info"
    
    try {
        # Generate random RAM size if no profile provided
        if (-not $MemoryProfile) {
            $ramInfo = Get-RandomRAMSize
        } else {
            $ramInfo = @{
                SizeGB = [math]::Round($MemoryProfile.Capacity / 1024 / 1024 / 1024)
                SizeBytes = $MemoryProfile.Capacity
                SizeKB = $MemoryProfile.Capacity / 1024
                SizeMB = $MemoryProfile.Capacity / 1024 / 1024
            }
        }
        
        $totalModified = 0
        
        # Key registry locations for RAM size display
        $ramPaths = @(
            @{Path = "HKLM:\HARDWARE\DESCRIPTION\System"; Name = "TotalPhysicalMemory"; Value = $ramInfo.SizeBytes; Type = "QWord"},
            @{Path = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"; Name = "TotalPhysicalMemory"; Value = $ramInfo.SizeBytes; Type = "QWord"},
            @{Path = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"; Name = "InstalledMemory"; Value = $ramInfo.SizeKB; Type = "QWord"},
            @{Path = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion"; Name = "TotalPhysicalMemory"; Value = $ramInfo.SizeBytes; Type = "QWord"}
        )
        
        foreach ($location in $ramPaths) {
            if (-not (Test-Path $location.Path)) {
                New-Item -Path $location.Path -Force | Out-Null
            }
            
            try {
                Set-ItemProperty -Path $location.Path -Name $location.Name -Value $location.Value -Type $location.Type -Force
                Write-ModuleLog "Set RAM size to $($ramInfo.SizeGB)GB at $($location.Path)\$($location.Name)" "Debug"
                $totalModified++
            } catch {
                Write-ModuleLog "Failed to update RAM at $($location.Path): $($_.Exception.Message)" "Warning"
            }
        }
        
        # Update WMI-related registry entries
        $wmiPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\WMI\Perfs\009\Memory",
            "HKLM:\SYSTEM\CurrentControlSet\Services\PerfOS\Performance"
        )
        
        foreach ($wmiPath in $wmiPaths) {
            if (-not (Test-Path $wmiPath)) {
                New-Item -Path $wmiPath -Force | Out-Null
            }
            
            try {
                Set-ItemProperty -Path $wmiPath -Name "TotalVisibleMemorySize" -Value $ramInfo.SizeKB -Force
                Set-ItemProperty -Path $wmiPath -Name "TotalPhysicalMemory" -Value $ramInfo.SizeBytes -Force
                $totalModified++
            } catch {
                # Continue if WMI paths fail
            }
        }
        
        Write-ModuleLog "RAM spoofing completed: $totalModified modifications, RAM set to $($ramInfo.SizeGB)GB" "Info"
        return @{ Success = $true; ModifiedCount = $totalModified; Message = "RAM updated to $($ramInfo.SizeGB)GB"; RAMSize = $ramInfo }
    }
    catch {
        Write-ModuleLog "RAM spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Invoke-GPUMemorySpoofing {
    <#
    .SYNOPSIS
        Updates GPU memory size to match spoofed graphics card
    #>
    [CmdletBinding()]
    param(
        [hashtable]$GPUProfile
    )
    
    Write-ModuleLog "Updating GPU memory size to match spoofed graphics card..." "Info"
    
    try {
        $totalModified = 0
        $vramSizeMB = [math]::Round($GPUProfile.VRAM / 1024 / 1024)
        
        # GPU memory registry locations
        $gpuMemoryPaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}",
            "HKLM:\SYSTEM\CurrentControlSet\Control\Video"
        )
        
        foreach ($basePath in $gpuMemoryPaths) {
            if (Test-Path $basePath) {
                $subKeys = Get-ChildItem -Path $basePath -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    if ($subKey.Name -match '\d{4}$') {
                        $fullPath = $subKey.PSPath
                        try {
                            Set-ItemProperty -Path $fullPath -Name "HardwareInformation.MemorySize" -Value $GPUProfile.VRAM -Force -ErrorAction SilentlyContinue
                            Set-ItemProperty -Path $fullPath -Name "HardwareInformation.AdapterRAM" -Value $GPUProfile.VRAM -Force -ErrorAction SilentlyContinue
                            Set-ItemProperty -Path $fullPath -Name "VideoMemorySize" -Value $GPUProfile.VRAM -Force -ErrorAction SilentlyContinue
                            $totalModified++
                        } catch {
                            # Continue if specific GPU entries fail
                        }
                    }
                }
            }
        }
        
        # System information registry
        $sysInfoPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"
        if (-not (Test-Path $sysInfoPath)) {
            New-Item -Path $sysInfoPath -Force | Out-Null
        }
        
        try {
            Set-ItemProperty -Path $sysInfoPath -Name "GraphicsMemory" -Value $GPUProfile.VRAM -Force
            Set-ItemProperty -Path $sysInfoPath -Name "DedicatedVideoMemory" -Value $GPUProfile.VRAM -Force
            $totalModified++
        } catch {
            Write-ModuleLog "Failed to update system information GPU memory" "Warning"
        }
        
        Write-ModuleLog "GPU memory spoofing completed: $totalModified modifications, VRAM set to ${vramSizeMB}MB" "Info"
        return @{ Success = $true; ModifiedCount = $totalModified; Message = "GPU memory updated to ${vramSizeMB}MB" }
    }
    catch {
        Write-ModuleLog "GPU memory spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Invoke-StorageCapacitySpoofing {
    <#
    .SYNOPSIS
        Updates storage device sizes to reflect spoofed storage devices
    #>
    [CmdletBinding()]
    param(
        [hashtable]$StorageProfile
    )
    
    Write-ModuleLog "Updating storage capacity to match spoofed storage device..." "Info"
    
    try {
        $totalModified = 0
        $capacityGB = [math]::Round($StorageProfile.Capacity / 1024 / 1024 / 1024)
        
        # Storage capacity registry locations
        $storagePaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Enum\STORAGE",
            "HKLM:\SYSTEM\CurrentControlSet\Services\disk\Enum"
        )
        
        foreach ($basePath in $storagePaths) {
            if (Test-Path $basePath) {
                $subKeys = Get-ChildItem -Path $basePath -Recurse -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    try {
                        $properties = Get-ItemProperty -Path $subKey.PSPath -ErrorAction SilentlyContinue
                        if ($properties -and ($properties.PSChildName -like "*Disk*" -or $properties.FriendlyName -like "*Disk*")) {
                            Set-ItemProperty -Path $subKey.PSPath -Name "Size" -Value $StorageProfile.Capacity -Force -ErrorAction SilentlyContinue
                            Set-ItemProperty -Path $subKey.PSPath -Name "Capacity" -Value $StorageProfile.Capacity -Force -ErrorAction SilentlyContinue
                            $totalModified++
                        }
                    } catch {
                        # Continue if specific storage entries fail
                    }
                }
            }
        }
        
        # System information registry
        $sysInfoPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"
        if (-not (Test-Path $sysInfoPath)) {
            New-Item -Path $sysInfoPath -Force | Out-Null
        }
        
        try {
            Set-ItemProperty -Path $sysInfoPath -Name "TotalStorageCapacity" -Value $StorageProfile.Capacity -Force
            $totalModified++
        } catch {
            Write-ModuleLog "Failed to update system information storage capacity" "Warning"
        }
        
        Write-ModuleLog "Storage capacity spoofing completed: $totalModified modifications, capacity set to ${capacityGB}GB" "Info"
        return @{ Success = $true; ModifiedCount = $totalModified; Message = "Storage capacity updated to ${capacityGB}GB" }
    }
    catch {
        Write-ModuleLog "Storage capacity spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Invoke-SystemInformationSpoofing {
    <#
    .SYNOPSIS
        Main function to orchestrate all system information spoofing operations
    #>
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [hashtable]$HardwareProfile
    )

    Write-ModuleLog "Starting comprehensive system information spoofing..." "Info"

    try {
        $results = @()
        $totalModified = 0

        # CPU Core Count Spoofing
        if ($HardwareProfile.CPU) {
            Write-ModuleLog "Updating CPU core count specifications..." "Info"
            $cpuResult = Invoke-CPUCoreCountSpoofing -CPUProfile $HardwareProfile.CPU
            $results += $cpuResult
            if ($cpuResult.Success) {
                $totalModified += $cpuResult.ModifiedCount
            }
        }

        # RAM Spoofing
        Write-ModuleLog "Updating RAM specifications..." "Info"
        $ramResult = Invoke-RAMSpoofing -MemoryProfile $HardwareProfile.Memory
        $results += $ramResult
        if ($ramResult.Success) {
            $totalModified += $ramResult.ModifiedCount
        }

        # GPU Memory Spoofing
        if ($HardwareProfile.GPU) {
            Write-ModuleLog "Updating GPU memory specifications..." "Info"
            $gpuResult = Invoke-GPUMemorySpoofing -GPUProfile $HardwareProfile.GPU
            $results += $gpuResult
            if ($gpuResult.Success) {
                $totalModified += $gpuResult.ModifiedCount
            }
        }

        # Storage Capacity Spoofing
        if ($HardwareProfile.Storage) {
            Write-ModuleLog "Updating storage capacity specifications..." "Info"
            $storageResult = Invoke-StorageCapacitySpoofing -StorageProfile $HardwareProfile.Storage
            $results += $storageResult
            if ($storageResult.Success) {
                $totalModified += $storageResult.ModifiedCount
            }
        }

        # Additional System Properties updates
        Write-ModuleLog "Updating additional system properties..." "Info"
        $additionalResult = Invoke-AdditionalSystemPropertiesUpdate -HardwareProfile $HardwareProfile
        $results += $additionalResult
        if ($additionalResult.Success) {
            $totalModified += $additionalResult.ModifiedCount
        }

        $successCount = ($results | Where-Object { $_.Success }).Count
        $totalOperations = $results.Count

        Write-ModuleLog "System information spoofing completed: $successCount/$totalOperations operations successful, $totalModified total modifications" "Info"

        return @{
            Success = $successCount -eq $totalOperations
            Results = $results
            TotalModifications = $totalModified
            Message = "System information spoofing: $successCount/$totalOperations operations successful"
        }
    }
    catch {
        Write-ModuleLog "System information spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Invoke-AdditionalSystemPropertiesUpdate {
    <#
    .SYNOPSIS
        Updates additional system properties for comprehensive spoofing
    #>
    [CmdletBinding()]
    param(
        [hashtable]$HardwareProfile
    )

    try {
        $totalModified = 0

        # System Information registry path
        $sysInfoPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"
        if (-not (Test-Path $sysInfoPath)) {
            New-Item -Path $sysInfoPath -Force | Out-Null
        }

        # Update system manufacturer and model
        if ($HardwareProfile.Motherboard) {
            Set-ItemProperty -Path $sysInfoPath -Name "SystemManufacturer" -Value $HardwareProfile.Motherboard.Manufacturer -Force
            Set-ItemProperty -Path $sysInfoPath -Name "SystemProductName" -Value $HardwareProfile.Motherboard.Model -Force
            Set-ItemProperty -Path $sysInfoPath -Name "SystemVersion" -Value $HardwareProfile.Motherboard.Version -Force
            $totalModified += 3
        }

        # Update BIOS information
        if ($HardwareProfile.Motherboard) {
            Set-ItemProperty -Path $sysInfoPath -Name "BIOSVendor" -Value $HardwareProfile.Motherboard.BIOSVendor -Force
            Set-ItemProperty -Path $sysInfoPath -Name "BIOSVersion" -Value $HardwareProfile.Motherboard.BIOSVersion -Force
            Set-ItemProperty -Path $sysInfoPath -Name "BIOSReleaseDate" -Value $HardwareProfile.Motherboard.BIOSDate -Force
            $totalModified += 3
        }

        # Update system UUID and machine GUID
        if ($HardwareProfile.UUID) {
            Set-ItemProperty -Path $sysInfoPath -Name "SystemUUID" -Value $HardwareProfile.UUID -Force
            $totalModified++
        }

        if ($HardwareProfile.MachineGUID) {
            $cryptoPath = "HKLM:\SOFTWARE\Microsoft\Cryptography"
            if (Test-Path $cryptoPath) {
                Set-ItemProperty -Path $cryptoPath -Name "MachineGuid" -Value $HardwareProfile.MachineGUID -Force
                $totalModified++
            }
        }

        # Update Windows NT CurrentVersion with hardware info
        $ntVersionPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion"
        if (Test-Path $ntVersionPath) {
            if ($HardwareProfile.CPU) {
                Set-ItemProperty -Path $ntVersionPath -Name "ProcessorNameString" -Value $HardwareProfile.CPU.Name -Force
                $totalModified++
            }
        }

        Write-ModuleLog "Additional system properties update completed: $totalModified modifications" "Debug"
        return @{ Success = $true; ModifiedCount = $totalModified; Message = "Additional system properties updated" }
    }
    catch {
        Write-ModuleLog "Additional system properties update failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Export functions
Export-ModuleMember -Function @(
    'Get-RandomRAMSize',
    'Invoke-CPUCoreCountSpoofing',
    'Invoke-RAMSpoofing',
    'Invoke-GPUMemorySpoofing',
    'Invoke-StorageCapacitySpoofing',
    'Invoke-SystemInformationSpoofing',
    'Invoke-AdditionalSystemPropertiesUpdate'
)

# Module initialization
Write-Verbose "System Information Spoofing Module loaded successfully"
