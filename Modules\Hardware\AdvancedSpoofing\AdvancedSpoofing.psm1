# Advanced Spoofing Module
# Contains all advanced spoofing functions for hardware components

# Import required modules
Import-Module "$PSScriptRoot\..\HardwareProfiles\HardwareProfiles.psm1" -Force
Import-Module "$PSScriptRoot\..\SerialNumberGenerator\SerialNumberGenerator.psm1" -Force
Import-Module "$PSScriptRoot\..\..\Core\Logging\Logging.psm1" -Force
Import-Module "$PSScriptRoot\..\..\Core\Utilities\Utilities.psm1" -Force

#region Advanced CPU Spoofing

function Invoke-AdvancedCPUSpoofing {
    <#
    .SYNOPSIS
        Comprehensive CPU spoofing using proven working method from CompleteCPUSpoofer.ps1
    #>
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [hashtable]$HardwareProfile
    )

    Write-ModuleLog "Starting comprehensive CPU spoofing with proven working method..." "Info"

    try {
        $cpuProfile = $HardwareProfile.CPU
        $totalModified = 0

        # Phase 1: Hardware Registry Modification (affects both Device Manager and System Properties)
        Write-ModuleLog "Phase 1: Hardware Registry Modification..." "Info"

        # First, remove any existing CPU cores beyond what we want to spoof
        for ($i = $cpuProfile.Cores; $i -lt 32; $i++) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$i"
            if (Test-Path $cpuPath) {
                try {
                    Remove-Item -Path $cpuPath -Recurse -Force -ErrorAction SilentlyContinue
                    Write-ModuleLog "Removed excess CPU core $i" "Debug"
                } catch {
                    # Continue if removal fails
                }
            }
        }

        # Now create/modify CPU cores according to the spoofed profile
        for ($i = 0; $i -lt $cpuProfile.Cores; $i++) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$i"

            # Create the path if it doesn't exist
            if (-not (Test-Path $cpuPath)) {
                New-Item -Path $cpuPath -Force | Out-Null
            }

            try {
                # Use the proven working approach - direct modification with Force parameter
                Set-ItemProperty -Path $cpuPath -Name "ProcessorNameString" -Value $cpuProfile.Name -Force
                Set-ItemProperty -Path $cpuPath -Name "Identifier" -Value "Intel64 Family $($cpuProfile.Family) Model $($cpuProfile.Model) Stepping $($cpuProfile.Stepping)" -Force
                Set-ItemProperty -Path $cpuPath -Name "VendorIdentifier" -Value "GenuineIntel" -Force
                Set-ItemProperty -Path $cpuPath -Name "~MHz" -Value ([int]$cpuProfile.BaseFreq) -Type DWord -Force

                Write-ModuleLog "Core $i - Modified successfully" "Debug"
                $totalModified++
            } catch {
                Write-ModuleLog "Core $i - Failed: $($_.Exception.Message)" "Warning"
            }
        }

        # Phase 2: Device Manager specific modifications
        Write-ModuleLog "Phase 2: Device Manager Modifications..." "Info"

        # Modify Device Class entries
        $processorClass = Get-ChildItem "HKLM:\SYSTEM\CurrentControlSet\Control\Class" -ErrorAction SilentlyContinue |
            Where-Object {
                $class = Get-ItemProperty $_.PSPath -ErrorAction SilentlyContinue
                $class.Class -eq "Processor"
            }

        if ($processorClass) {
            $devices = Get-ChildItem $processorClass.PSPath -ErrorAction SilentlyContinue
            foreach ($device in $devices) {
                try {
                    Set-ItemProperty -Path $device.PSPath -Name "FriendlyName" -Value $cpuProfile.Name -Force -ErrorAction SilentlyContinue
                    Set-ItemProperty -Path $device.PSPath -Name "DeviceDesc" -Value $cpuProfile.Name -Force -ErrorAction SilentlyContinue
                    Write-ModuleLog "Device class updated for Device Manager" "Debug"
                    $totalModified++
                } catch {
                    # Skip protected entries
                }
            }
        }

        # Modify ACPI Enum entries
        $enumPaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Enum\ACPI",
            "HKLM:\SYSTEM\CurrentControlSet\Enum\Root\ACPI"
        )

        foreach ($enumPath in $enumPaths) {
            if (Test-Path $enumPath) {
                $cpuDevices = Get-ChildItem $enumPath -ErrorAction SilentlyContinue |
                    Where-Object { $_.PSChildName -match "Processor|CPU|GenuineIntel|AuthenticAMD" }

                foreach ($device in $cpuDevices) {
                    $instances = Get-ChildItem $device.PSPath -ErrorAction SilentlyContinue
                    foreach ($instance in $instances) {
                        try {
                            Set-ItemProperty -Path $instance.PSPath -Name "FriendlyName" -Value $cpuProfile.Name -Force -ErrorAction SilentlyContinue
                            Set-ItemProperty -Path $instance.PSPath -Name "DeviceDesc" -Value $cpuProfile.Name -Force -ErrorAction SilentlyContinue
                            $totalModified++
                        } catch {
                            # Skip protected entries
                        }
                    }
                }
            }
        }

        # Phase 3: System Properties specific modifications
        Write-ModuleLog "Phase 3: System Properties Modifications..." "Info"

        # Environment variables
        try {
            $envPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Environment"
            $identifier = "Intel64 Family $($cpuProfile.Family) Model $($cpuProfile.Model) Stepping $($cpuProfile.Stepping)"
            Set-ItemProperty -Path $envPath -Name "PROCESSOR_IDENTIFIER" -Value $identifier -Force
            Set-ItemProperty -Path $envPath -Name "NUMBER_OF_PROCESSORS" -Value $cpuProfile.Cores -Force
            Set-ItemProperty -Path $envPath -Name "PROCESSOR_LEVEL" -Value $cpuProfile.Family -Force
            Set-ItemProperty -Path $envPath -Name "PROCESSOR_REVISION" -Value ($cpuProfile.Model * 256 + $cpuProfile.Stepping).ToString("x4") -Force

            # Update environment variables for immediate effect
            [Environment]::SetEnvironmentVariable("PROCESSOR_IDENTIFIER", $identifier, "Machine")
            [Environment]::SetEnvironmentVariable("NUMBER_OF_PROCESSORS", $cpuProfile.Cores.ToString(), "Machine")
            [Environment]::SetEnvironmentVariable("PROCESSOR_LEVEL", $cpuProfile.Family.ToString(), "Machine")
            [Environment]::SetEnvironmentVariable("PROCESSOR_REVISION", ($cpuProfile.Model * 256 + $cpuProfile.Stepping).ToString("x4"), "Machine")

            Write-ModuleLog "Environment variables updated for System Properties (Cores: $($cpuProfile.Cores))" "Debug"
            $totalModified++
        } catch {
            Write-ModuleLog "Environment variables update failed" "Warning"
        }

        # System Information registry
        try {
            $sysInfoPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"
            if (!(Test-Path $sysInfoPath)) {
                New-Item -Path $sysInfoPath -Force | Out-Null
            }

            New-ItemProperty -Path $sysInfoPath -Name "ProcessorNameString" -Value $cpuProfile.Name -PropertyType String -Force | Out-Null
            New-ItemProperty -Path $sysInfoPath -Name "ProcessorSpeed" -Value ([int]$cpuProfile.BaseFreq) -PropertyType DWord -Force | Out-Null
            Write-ModuleLog "System information registry updated" "Debug"
            $totalModified++
        } catch {
            Write-ModuleLog "System information registry update failed" "Warning"
        }

        # Phase 4: WMI modification attempt
        Write-ModuleLog "Phase 4: WMI Modifications..." "Info"
        try {
            # Stop WMI
            Stop-Service "WinMgmt" -Force -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2

            # Create MOF for WMI modification
            $mofContent = @"
#pragma namespace("\\\\.\\root\\cimv2")
instance of __Win32Provider as `$P
{
    Name = "CIMWin32";
};
instance of __InstanceModificationEvent
{
    TargetInstance = instance of Win32_Processor
    {
        Name = "$($cpuProfile.Name)";
        Description = "$($cpuProfile.Name)";
        MaxClockSpeed = $([int]$cpuProfile.BaseFreq);
        NumberOfCores = $($cpuProfile.Cores);
        NumberOfLogicalProcessors = $($cpuProfile.Threads);
        ProcessorId = "Intel64 Family $($cpuProfile.Family) Model $($cpuProfile.Model) Stepping $($cpuProfile.Stepping)";
    };
};
"@

            $mofFile = "$env:TEMP\CompleteCPU.mof"
            $mofContent | Out-File $mofFile -Encoding ASCII
            mofcomp $mofFile 2>$null
            Remove-Item $mofFile -Force -ErrorAction SilentlyContinue

            # Restart WMI
            Start-Service "WinMgmt" -ErrorAction SilentlyContinue
            Write-ModuleLog "WMI modification attempted" "Debug"
            $totalModified++
        } catch {
            Write-ModuleLog "WMI update failed (this is normal): $($_.Exception.Message)" "Warning"
        }

        # Phase 5: System refresh
        Write-ModuleLog "Phase 5: System Refresh..." "Info"
        try {
            # Close Device Manager if open
            Get-Process "mmc" -ErrorAction SilentlyContinue | Where-Object {$_.MainWindowTitle -like "*Device Manager*"} | Stop-Process -Force -ErrorAction SilentlyContinue

            # Restart services
            Restart-Service "PlugPlay" -Force -ErrorAction SilentlyContinue
            Restart-Service "SystemEventsBroker" -Force -ErrorAction SilentlyContinue

            # Force hardware rescan
            Start-Process "pnputil.exe" -ArgumentList "/scan-devices" -Wait -NoNewWindow -ErrorAction SilentlyContinue

            # Restart explorer for System Properties refresh
            Stop-Process -Name "explorer" -Force -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
            Start-Process "explorer.exe" -ErrorAction SilentlyContinue

            Write-ModuleLog "System refresh completed" "Debug"
        } catch {
            Write-ModuleLog "Some refresh operations failed" "Warning"
        }

        Write-ModuleLog "Comprehensive CPU spoofing completed: $totalModified entries modified" "Info"
        return @{ Success = $true; Message = "CPU spoofed to $($cpuProfile.Name) ($($cpuProfile.Cores) cores) - $totalModified modifications" }
    }
    catch {
        Write-ModuleLog "Advanced CPU spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

#endregion

#region Advanced GPU Spoofing

function Invoke-AdvancedGPUSpoofing {
    <#
    .SYNOPSIS
        Aggressive GPU spoofing that specifically targets and replaces VMware graphics components
    #>
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [hashtable]$HardwareProfile
    )
    
    Write-ModuleLog "Starting aggressive VMware GPU spoofing..." "Info"
    
    try {
        $gpuProfile = $HardwareProfile.GPU
        $modifiedCount = 0
        
        # AGGRESSIVE: Target ALL display adapters, especially VMware ones
        $displayClassKey = 'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}'
        
        if (Test-Path $displayClassKey) {
            $subKeys = Get-ChildItem -Path $displayClassKey -ErrorAction SilentlyContinue
            foreach ($subKey in $subKeys) {
                if ($subKey.Name -match '\d{4}$') {
                    $fullPath = $subKey.PSPath
                    
                    try {
                        # AGGRESSIVELY replace ALL display adapters with realistic GPU
                        Set-RegistryValue -Path $fullPath -Name "DriverDesc" -Value $gpuProfile.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "ProviderName" -Value $gpuProfile.Vendor -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "DriverVersion" -Value $gpuProfile.DriverVersion -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Class" -Value "Display" -Type "String"
                        
                        # Hardware information
                        Set-RegistryValue -Path $fullPath -Name "HardwareInformation.ChipType" -Value $gpuProfile.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "HardwareInformation.MemorySize" -Value ($gpuProfile.VRAM * 1024 * 1024) -Type "QWord"
                        Set-RegistryValue -Path $fullPath -Name "HardwareInformation.AdapterString" -Value $gpuProfile.Model -Type "String"
                        
                        # Remove any VMware-specific values
                        $vmwareValues = @("VMware", "SVGA", "Virtual", "vm")
                        $properties = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                        if ($properties) {
                            foreach ($prop in $properties.PSObject.Properties) {
                                if ($prop.Value -and $prop.Value.ToString() -match "VMware|SVGA|Virtual|vm") {
                                    Set-RegistryValue -Path $fullPath -Name $prop.Name -Value $gpuProfile.Model -Type "String"
                                }
                            }
                        }
                        
                        $modifiedCount++
                        Write-ModuleLog "Replaced display adapter: $fullPath" "Debug"
                    }
                    catch {
                        $errorMsg = $_.Exception.Message
                        Write-ModuleLog "Failed to modify display adapter ${fullPath}: $errorMsg" "Warning"
                    }
                }
            }
        }
        
        # AGGRESSIVE: Target ALL PCI devices and replace VMware ones
        $pciPath = 'HKLM:\SYSTEM\CurrentControlSet\Enum\PCI'
        if (Test-Path $pciPath) {
            $pciKeys = Get-ChildItem -Path $pciPath -Recurse -ErrorAction SilentlyContinue
            foreach ($pciKey in $pciKeys) {
                $fullPath = $pciKey.PSPath
                
                try {
                    $properties = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                    
                    # Target VMware-specific entries OR any display devices
                    if ($properties -and (
                        $fullPath -like "*VEN_15AD*" -or  # VMware vendor ID
                        $properties.DeviceDesc -like "*VMware*" -or 
                        $properties.DeviceDesc -like "*SVGA*" -or
                        $properties.Mfg -like "*VMware*" -or
                        $properties.Class -eq "Display"
                    )) {
                        
                        # Replace with realistic GPU information
                        Set-RegistryValue -Path $fullPath -Name "DeviceDesc" -Value $gpuProfile.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Mfg" -Value $gpuProfile.Vendor -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Class" -Value "Display" -Type "String"
                        
                        # Generate realistic PCI hardware ID
                        $vendorId = if ($gpuProfile.Vendor -like "*NVIDIA*") { "10DE" } else { "1002" }
                        $deviceId = "PCI\VEN_$vendorId&DEV_$($gpuProfile.DeviceID)"
                        Set-RegistryValue -Path $fullPath -Name "HardwareID" -Value @($deviceId) -Type "MultiString"
                        
                        # Compatible IDs
                        $compatibleIds = @(
                            "PCI\VEN_$vendorId&DEV_$($gpuProfile.DeviceID)&SUBSYS_00000000",
                            "PCI\VEN_$vendorId&DEV_$($gpuProfile.DeviceID)",
                            "PCI\VEN_$vendorId&CC_030000",
                            "PCI\VEN_$vendorId",
                            "PCI\CC_030000"
                        )
                        Set-RegistryValue -Path $fullPath -Name "CompatibleIDs" -Value $compatibleIds -Type "MultiString"
                        
                        # Driver information
                        Set-RegistryValue -Path $fullPath -Name "DriverVersion" -Value $gpuProfile.DriverVersion -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "DriverDate" -Value "7-19-2024" -Type "String"
                        
                        $modifiedCount++
                        Write-ModuleLog "Aggressively replaced VMware device: $fullPath" "Debug"
                    }
                }
                catch {
                    $errorMsg = $_.Exception.Message
                    Write-ModuleLog "Failed to modify PCI device ${fullPath}: $errorMsg" "Warning"
                }
            }
        }
        
        # Target VMware SVGA entries specifically
        $svgaPaths = @(
            'HKLM:\SYSTEM\CurrentControlSet\Enum\PCI\VEN_15AD*',
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}'
        )
        
        foreach ($basePath in $svgaPaths) {
            $matchingKeys = Get-ChildItem -Path $basePath.Replace('*', '') -Recurse -ErrorAction SilentlyContinue | Where-Object { $_.PSPath -like "*15AD*" -or $_.PSPath -like "*VMware*" -or $_.PSPath -like "*SVGA*" }
            
            foreach ($vmwareKey in $matchingKeys) {
                try {
                    $fullPath = $vmwareKey.PSPath
                    
                    # Completely replace VMware SVGA with realistic GPU
                    Set-RegistryValue -Path $fullPath -Name "DeviceDesc" -Value $gpuProfile.Model -Type "String"
                    Set-RegistryValue -Path $fullPath -Name "DriverDesc" -Value $gpuProfile.Model -Type "String"
                    Set-RegistryValue -Path $fullPath -Name "Mfg" -Value $gpuProfile.Vendor -Type "String"
                    Set-RegistryValue -Path $fullPath -Name "ProviderName" -Value $gpuProfile.Vendor -Type "String"
                    Set-RegistryValue -Path $fullPath -Name "DriverVersion" -Value $gpuProfile.DriverVersion -Type "String"
                    
                    # Replace hardware IDs
                    $vendorId = if ($gpuProfile.Vendor -like "*NVIDIA*") { "10DE" } else { "1002" }
                    $newHwId = "PCI\VEN_$vendorId&DEV_$($gpuProfile.DeviceID)"
                    Set-RegistryValue -Path $fullPath -Name "HardwareID" -Value @($newHwId) -Type "MultiString"
                    
                    $modifiedCount++
                    Write-ModuleLog "Replaced VMware SVGA device: $fullPath" "Info"
                }
                catch {
                    Write-ModuleLog "Failed to replace VMware device $($vmwareKey.PSPath): $($_.Exception.Message)" "Warning"
                }
            }
        }
        
        Write-ModuleLog "Aggressive GPU spoofing completed: $modifiedCount VMware entries replaced" "Info"
        return @{ Success = $true; Message = "Aggressively replaced $modifiedCount VMware GPU entries with $($gpuProfile.Model)" }
    }
    catch {
        Write-ModuleLog "Advanced GPU spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

#endregion

#region Advanced Storage Spoofing

function Invoke-AdvancedStorageSpoofing {
    <#
    .SYNOPSIS
        Aggressive storage spoofing that targets VMware virtual disks and replaces them with realistic storage
    #>
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [hashtable]$HardwareProfile
    )
    
    Write-ModuleLog "Starting aggressive VMware storage spoofing..." "Info"
    
    try {
        $storageProfile = $HardwareProfile.Storage
        $modifiedCount = 0
        
        # Method 1: Direct registry path spoofing for VMware NVMe disks (proven to work)
        Write-ModuleLog "Searching for VMware NVMe disks..." "Debug"
        
        $vmwareNVMePaths = @(
            'HKLM:\SYSTEM\CurrentControlSet\Enum\SCSI\Disk&Ven_NVMe&Prod_VMware_Virtual_N'
        )
        
        foreach ($basePath in $vmwareNVMePaths) {
            if (Test-Path $basePath) {
                $subKeys = Get-ChildItem -Path $basePath -Recurse -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    if ($subKey.PSChildName -notmatch "Properties|Device Parameters|Volatile") {
                        $fullPath = $subKey.PSPath
                        $props = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                        
                        if ($props.FriendlyName -like "*VMware*" -or $props.FriendlyName -like "*Virtual*") {
                            Write-ModuleLog "Found VMware NVMe disk at: $fullPath" "Info"
                            Write-ModuleLog "Current FriendlyName: $($props.FriendlyName)" "Debug"
                            
                            # Update FriendlyName
                            Set-ItemProperty -Path $fullPath -Name "FriendlyName" -Value $storageProfile.Model -Force
                            Write-ModuleLog "Changed FriendlyName to: $($storageProfile.Model)" "Info"
                            
                            # Update DeviceDesc if present
                            if ($props.DeviceDesc) {
                                Set-ItemProperty -Path $fullPath -Name "DeviceDesc" -Value $storageProfile.Model -Force
                                Write-ModuleLog "Changed DeviceDesc to: $($storageProfile.Model)" "Info"
                            }
                            
                            $modifiedCount++
                        }
                    }
                }
            }
        }
        
        # AGGRESSIVE: Target ALL storage devices, especially VMware ones
        $storageKeys = @(
            'HKLM:\SYSTEM\CurrentControlSet\Enum\SCSI',
            'HKLM:\SYSTEM\CurrentControlSet\Enum\IDE',
            'HKLM:\SYSTEM\CurrentControlSet\Enum\STORAGE'
        )
        
        foreach ($keyPath in $storageKeys) {
            if (Test-Path $keyPath) {
                $subKeys = Get-ChildItem -Path $keyPath -Recurse -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    $fullPath = $subKey.PSPath
                    
                    try {
                        # AGGRESSIVELY replace ALL storage devices, especially VMware ones
                        $properties = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                        
                        # Target VMware storage OR any storage device
                        if ($properties -and (
                            $fullPath -like "*VMware*" -or
                            $properties.DeviceDesc -like "*VMware*" -or
                            $properties.DeviceDesc -like "*Virtual*" -or
                            $properties.Mfg -like "*VMware*" -or
                            $properties.Class -eq "DiskDrive" -or 
                            $properties.DeviceDesc -like "*disk*" -or 
                            $properties.DeviceDesc -like "*drive*"
                        )) {
                            
                            # Replace with realistic storage device
                            Set-RegistryValue -Path $fullPath -Name "FriendlyName" -Value $storageProfile.Model -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "DeviceDesc" -Value $storageProfile.Model -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "Mfg" -Value $storageProfile.Manufacturer -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "Class" -Value "DiskDrive" -Type "String"
                            
                            # Generate realistic storage serial
                            $storageSerial = New-RealisticSerialNumber -Manufacturer $storageProfile.Manufacturer -DeviceType "Storage"
                            Set-RegistryValue -Path $fullPath -Name "SerialNumber" -Value $storageSerial -Type "String"
                            
                            # Firmware and revision information
                            Set-RegistryValue -Path $fullPath -Name "FirmwareRevision" -Value $storageProfile.FirmwareVersion -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "ProductRevision" -Value $storageProfile.FirmwareVersion -Type "String"
                            
                            # Interface and connection type
                            Set-RegistryValue -Path $fullPath -Name "BusType" -Value $storageProfile.Interface -Type "String"
                            Set-RegistryValue -Path $fullPath -Name "MediaType" -Value "Fixed hard disk media" -Type "String"
                            
                            # SMART attributes (realistic values)
                            $smartAttributes = @{
                                "Temperature" = Get-Random -Min 25 -Max 45
                                "PowerOnHours" = Get-Random -Min 100 -Max 8760
                                "PowerCycleCount" = Get-Random -Min 50 -Max 1000
                                "ReallocatedSectors" = 0
                                "CurrentPendingSectors" = 0
                            }
                            
                            foreach ($smart in $smartAttributes.GetEnumerator()) {
                                Set-RegistryValue -Path $fullPath -Name "SMART_$($smart.Key)" -Value $smart.Value -Type "DWord"
                            }
                            
                            # Remove VMware-specific values
                            if ($properties) {
                                foreach ($prop in $properties.PSObject.Properties) {
                                    if ($prop.Value -and $prop.Value.ToString() -match "VMware|Virtual|vm") {
                                        Set-RegistryValue -Path $fullPath -Name $prop.Name -Value $storageProfile.Model -Type "String"
                                    }
                                }
                            }
                            
                            $modifiedCount++
                            Write-ModuleLog "Replaced storage device: $fullPath" "Debug"
                        }
                    }
                    catch {
                        $errorMsg = $_.Exception.Message
                        Write-ModuleLog "Failed to modify storage device ${fullPath}: $errorMsg" "Warning"
                    }
                }
            }
        }
        
        # Spoof storage controller information
        $controllerPaths = @(
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}',  # System devices
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e96a-e325-11ce-bfc1-08002be10318}'   # HDC controllers
        )
        
        foreach ($controllerPath in $controllerPaths) {
            if (Test-Path $controllerPath) {
                $subKeys = Get-ChildItem -Path $controllerPath -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    if ($subKey.Name -match '\d{4}$') {
                        $fullPath = $subKey.PSPath
                        
                        # Intel storage controller information
                        Set-RegistryValue -Path $fullPath -Name "DriverDesc" -Value "Intel(R) Volume Management Device NVMe RAID Controller" -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "ProviderName" -Value "Intel Corporation" -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "DriverVersion" -Value "18.1.0.1006" -Type "String"
                        
                        $modifiedCount++
                    }
                }
            }
        }
        
        # Update WMI storage information in a safer location
        $wmiStoragePath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\DeviceAccess\Global\{C1C8FD98-9D82-4F2F-A7E1-D7F6098EB0CE}"
        if (-not (Test-Path $wmiStoragePath)) {
            try {
                New-Item -Path $wmiStoragePath -Force | Out-Null
                Set-RegistryValue -Path $wmiStoragePath -Name "DiskDrive" -Value $storageProfile.Model -Type "String"
                Set-RegistryValue -Path $wmiStoragePath -Name "MediaType" -Value "Fixed hard disk media" -Type "String"
                Set-RegistryValue -Path $wmiStoragePath -Name "InterfaceType" -Value $storageProfile.Interface -Type "String"
            }
            catch {
                # Skip WMI storage spoofing if path creation fails
                Write-ModuleLog "Skipping WMI storage spoofing - path creation failed" "Debug"
            }
        }
        
        Write-ModuleLog "Advanced storage spoofing completed: $modifiedCount entries modified" "Info"
        return @{ Success = $true; Message = "Storage spoofed to $($storageProfile.Model) ($($storageProfile.Interface))" }
    }
    catch {
        Write-ModuleLog "Advanced storage spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

#endregion

#region Advanced Audio Device Spoofing

function Invoke-AdvancedAudioSpoofing {
    <#
    .SYNOPSIS
        Comprehensive audio device spoofing including drivers and hardware information
    #>
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [hashtable]$HardwareProfile
    )
    
    Write-ModuleLog "Starting advanced audio spoofing..." "Info"
    
    try {
        $modifiedCount = 0
        
        # Use audio profile from hardware profile
        $selectedAudio = $HardwareProfile.Audio
        
        # Audio device registry paths
        $audioKeys = @(
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e96c-e325-11ce-bfc1-08002be10318}',  # Sound devices
            'HKLM:\SYSTEM\CurrentControlSet\Enum\HDAUDIO',
            'HKLM:\SYSTEM\CurrentControlSet\Enum\PCI'
        )
        
        foreach ($keyPath in $audioKeys) {
            if (Test-Path $keyPath) {
                $subKeys = Get-ChildItem -Path $keyPath -Recurse -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    $fullPath = $subKey.PSPath
                    
                    # Check if this is an audio-related key
                    $properties = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                    if ($properties -and ($properties.Class -eq "MEDIA" -or $fullPath -like "*HDAUDIO*" -or $fullPath -like "*Audio*")) {
                        
                        # Update audio device properties
                        Set-RegistryValue -Path $fullPath -Name "DeviceDesc" -Value $selectedAudio.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "DriverDesc" -Value $selectedAudio.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Mfg" -Value $selectedAudio.Vendor -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "ProviderName" -Value $selectedAudio.Vendor -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "DriverVersion" -Value $selectedAudio.DriverVersion -Type "String"
                        
                        # Audio codec information
                        Set-RegistryValue -Path $fullPath -Name "CodecName" -Value "ALC4080" -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "CodecVendor" -Value $selectedAudio.Vendor -Type "String"
                        
                        # Hardware IDs for audio
                        if ($selectedAudio.Vendor -like "*Realtek*") {
                            $hwId = "HDAUDIO\FUNC_01&VEN_10EC&DEV_$($selectedAudio.DeviceID)"
                        } elseif ($selectedAudio.Vendor -like "*Intel*") {
                            $hwId = "PCI\VEN_8086&DEV_$($selectedAudio.DeviceID)"
                        } else {
                            $hwId = "PCI\VEN_10DE&DEV_$($selectedAudio.DeviceID)"
                        }
                        
                        Set-RegistryValue -Path $fullPath -Name "HardwareID" -Value @($hwId) -Type "MultiString"
                        
                        $modifiedCount++
                    }
                }
            }
        }
        
        Write-ModuleLog "Advanced audio spoofing completed: $modifiedCount entries modified" "Info"
        return @{ Success = $true; Message = "Audio spoofed to $($selectedAudio.Model)" }
    }
    catch {
        Write-ModuleLog "Advanced audio spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

#endregion

#region Advanced USB Controller Spoofing

function Invoke-AdvancedUSBSpoofing {
    <#
    .SYNOPSIS
        Comprehensive USB controller and device spoofing
    #>
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [hashtable]$HardwareProfile
    )
    
    Write-ModuleLog "Starting advanced USB spoofing..." "Info"
    
    try {
        $modifiedCount = 0
        
        # Use USB controller from hardware profile
        $selectedController = $HardwareProfile.USB
        
        # USB controller registry paths
        $usbKeys = @(
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{36fc9e60-c465-11cf-8056-444553540000}',  # USB controllers
            'HKLM:\SYSTEM\CurrentControlSet\Enum\USB',
            'HKLM:\SYSTEM\CurrentControlSet\Enum\PCI'
        )
        
        foreach ($keyPath in $usbKeys) {
            if (Test-Path $keyPath) {
                $subKeys = Get-ChildItem -Path $keyPath -Recurse -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    $fullPath = $subKey.PSPath
                    
                    # Check if this is a USB-related key
                    $properties = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                    if ($properties -and ($properties.Class -eq "USB" -or $fullPath -like "*USB*" -or $properties.Service -eq "usbhub" -or $properties.DeviceDesc -like "*USB*" -or $properties.DeviceDesc -like "*Host Controller*")) {
                        
                        # Update USB controller properties
                        Set-RegistryValue -Path $fullPath -Name "DeviceDesc" -Value $selectedController.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "DriverDesc" -Value $selectedController.Model -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "Mfg" -Value $selectedController.Vendor -Type "String"
                        Set-RegistryValue -Path $fullPath -Name "ProviderName" -Value $selectedController.Vendor -Type "String"
                        
                        # Generate realistic USB controller serial
                        $usbSerial = New-RealisticSerialNumber -Manufacturer $selectedController.Vendor -DeviceType "USB"
                        Set-RegistryValue -Path $fullPath -Name "SerialNumber" -Value $usbSerial -Type "String"
                        
                        # USB hardware identifiers
                        $usbHwId = "PCI\VEN_$($selectedController.VendorID)&DEV_$($selectedController.DeviceID)"
                        Set-RegistryValue -Path $fullPath -Name "HardwareID" -Value @($usbHwId) -Type "MultiString"
                        
                        # USB specific properties based on controller version
                        Set-RegistryValue -Path $fullPath -Name "USBVersion" -Value $selectedController.USBVersion -Type "String"
                        
                        $speedValue = switch ($selectedController.USBVersion) {
                            "3.1" { "SuperSpeedPlus" }
                            "3.0" { "SuperSpeed" }
                            "2.0" { "HighSpeed" }
                            default { "SuperSpeed" }
                        }
                        Set-RegistryValue -Path $fullPath -Name "Speed" -Value $speedValue -Type "String"
                        
                        # Controller-specific driver version
                        $driverVersion = switch ($selectedController.Vendor) {
                            { $_ -like "*Intel*" } { "10.0.19041.3636" }
                            { $_ -like "*AMD*" } { "10.0.19041.3570" }
                            { $_ -like "*ASMedia*" } { "1.16.65.1" }
                            { $_ -like "*VIA*" } { "6.1.7600.16385" }
                            { $_ -like "*Renesas*" } { "3.0.23.0" }
                            default { "10.0.19041.3636" }
                        }
                        Set-RegistryValue -Path $fullPath -Name "DriverVersion" -Value $driverVersion -Type "String"
                        
                        # USB controller capabilities
                        Set-RegistryValue -Path $fullPath -Name "MaxPorts" -Value 8 -Type "DWord"
                        Set-RegistryValue -Path $fullPath -Name "PowerManagement" -Value 1 -Type "DWord"
                        
                        $modifiedCount++
                    }
                }
            }
        }
        
        # Create realistic USB device entries with detailed information
        $usbDevices = @(
            @{ VID = "046D"; PID = "C52B"; Name = "Logitech USB Input Device"; Manufacturer = "Logitech"; Service = "mouhid" },
            @{ VID = "413C"; PID = "2113"; Name = "Dell USB Keyboard"; Manufacturer = "Dell"; Service = "kbdhid" },
            @{ VID = "0781"; PID = "5583"; Name = "SanDisk USB Storage Device"; Manufacturer = "SanDisk"; Service = "usbstor" },
            @{ VID = "04F2"; PID = "B6EE"; Name = "Chicony USB2.0 Camera"; Manufacturer = "Chicony"; Service = "usbvideo" },
            @{ VID = "05AC"; PID = "12A8"; Name = "Apple USB SuperDrive"; Manufacturer = "Apple"; Service = "usbstor" }
        )
        
        foreach ($device in $usbDevices) {
            $devicePath = "HKLM:\SYSTEM\CurrentControlSet\Enum\USB\VID_$($device.VID)&PID_$($device.PID)"
            $instancePath = "$devicePath\$(Get-Random -Min 10000000 -Max 99999999)&0&0000"
            
            if (-not (Test-Path $devicePath)) {
                New-Item -Path $devicePath -Force | Out-Null
            }
            
            if (-not (Test-Path $instancePath)) {
                New-Item -Path $instancePath -Force | Out-Null
            }
            
            # Device description and manufacturer
            Set-RegistryValue -Path $instancePath -Name "DeviceDesc" -Value $device.Name -Type "String"
            Set-RegistryValue -Path $instancePath -Name "Mfg" -Value $device.Manufacturer -Type "String"
            Set-RegistryValue -Path $instancePath -Name "Service" -Value $device.Service -Type "String"
            Set-RegistryValue -Path $instancePath -Name "ConfigFlags" -Value 0 -Type "DWord"
            
            # Generate realistic device serial number
            $deviceSerial = New-RealisticSerialNumber -Manufacturer $device.Manufacturer -DeviceType "USB"
            Set-RegistryValue -Path $instancePath -Name "SerialNumber" -Value $deviceSerial -Type "String"
            
            # USB device hardware ID
            $deviceHwId = "USB\VID_$($device.VID)&PID_$($device.PID)"
            Set-RegistryValue -Path $instancePath -Name "HardwareID" -Value @($deviceHwId) -Type "MultiString"
            
            # Compatible IDs for USB devices
            $compatibleIds = @(
                "USB\Class_03&SubClass_01&Prot_01",
                "USB\Class_03&SubClass_01",
                "USB\Class_03"
            )
            Set-RegistryValue -Path $instancePath -Name "CompatibleIDs" -Value $compatibleIds -Type "MultiString"
            
            # Device capabilities
            Set-RegistryValue -Path $instancePath -Name "Capabilities" -Value 84 -Type "DWord"
            Set-RegistryValue -Path $instancePath -Name "UINumber" -Value (Get-Random -Min 1 -Max 99) -Type "DWord"
            
            $modifiedCount++
        }
        
        Write-ModuleLog "Advanced USB spoofing completed: $modifiedCount entries modified" "Info"
        return @{ Success = $true; Message = "USB controllers spoofed to $($selectedController.Model)" }
    }
    catch {
        Write-ModuleLog "Advanced USB spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

#endregion

#region SMBIOS and DMI Table Spoofing

function Invoke-SMBIOSSpoofing {
    <#
    .SYNOPSIS
        Spoofs SMBIOS/DMI table information to hide virtualization traces
    #>
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [hashtable]$HardwareProfile
    )
    
    Write-ModuleLog "Starting SMBIOS/DMI spoofing..." "Info"
    
    try {
        $modifiedCount = 0
        $motherboard = $HardwareProfile.Motherboard
        
        # SMBIOS registry locations
        $smbiosKeys = @(
            'HKLM:\HARDWARE\DESCRIPTION\System\BIOS',
            'HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation',
            'HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Group Policy\History',
            'HKLM:\SOFTWARE\Microsoft\Cryptography'
        )
        
        foreach ($keyPath in $smbiosKeys) {
            if (Test-Path $keyPath) {
                # System Information (SMBIOS Type 1)
                Set-RegistryValue -Path $keyPath -Name "SystemManufacturer" -Value $motherboard.Manufacturer -Type "String"
                Set-RegistryValue -Path $keyPath -Name "SystemProductName" -Value $motherboard.Model -Type "String"
                Set-RegistryValue -Path $keyPath -Name "SystemVersion" -Value "1.0" -Type "String"
                Set-RegistryValue -Path $keyPath -Name "SystemSerialNumber" -Value $HardwareProfile.SystemSerial -Type "String"
                Set-RegistryValue -Path $keyPath -Name "SystemUUID" -Value $HardwareProfile.UUID -Type "String"
                Set-RegistryValue -Path $keyPath -Name "SystemSKU" -Value "SKU_$(Get-Random -Min 1000 -Max 9999)" -Type "String"
                Set-RegistryValue -Path $keyPath -Name "SystemFamily" -Value "Desktop" -Type "String"
                
                # BIOS Information (SMBIOS Type 0)
                Set-RegistryValue -Path $keyPath -Name "BIOSVendor" -Value $motherboard.BIOSVendor -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BIOSVersion" -Value $motherboard.BIOSVersion -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BIOSReleaseDate" -Value $motherboard.BIOSDate -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BIOSMajorRelease" -Value 5 -Type "DWord"
                Set-RegistryValue -Path $keyPath -Name "BIOSMinorRelease" -Value 17 -Type "DWord"
                
                # Baseboard Information (SMBIOS Type 2)
                Set-RegistryValue -Path $keyPath -Name "BaseBoardManufacturer" -Value $motherboard.Manufacturer -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BaseBoardProduct" -Value $motherboard.Model -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BaseBoardVersion" -Value "Rev 1.xx" -Type "String"
                Set-RegistryValue -Path $keyPath -Name "BaseBoardSerialNumber" -Value (New-RealisticSerialNumber -Manufacturer $motherboard.Manufacturer -DeviceType "Motherboard") -Type "String"
                
                # Chassis Information (SMBIOS Type 3)
                Set-RegistryValue -Path $keyPath -Name "ChassisManufacturer" -Value $motherboard.Manufacturer -Type "String"
                Set-RegistryValue -Path $keyPath -Name "ChassisType" -Value "Desktop" -Type "String"
                Set-RegistryValue -Path $keyPath -Name "ChassisVersion" -Value "1.0" -Type "String"
                Set-RegistryValue -Path $keyPath -Name "ChassisSerialNumber" -Value (New-RealisticSerialNumber -Manufacturer $motherboard.Manufacturer -DeviceType "Chassis") -Type "String"
                
                $modifiedCount++
            }
        }
        
        # Machine GUID spoofing
        $machineGuidPath = "HKLM:\SOFTWARE\Microsoft\Cryptography"
        Set-RegistryValue -Path $machineGuidPath -Name "MachineGuid" -Value $HardwareProfile.MachineGUID -Type "String"
        
        Write-ModuleLog "SMBIOS/DMI spoofing completed: $modifiedCount entries modified" "Info"
        return @{ Success = $true; Message = "SMBIOS spoofed to $($motherboard.Manufacturer) $($motherboard.Model)" }
    }
    catch {
        Write-ModuleLog "SMBIOS spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

#endregion

#region Hardware Sensors Spoofing

function Invoke-HardwareSensorsSpoofing {
    <#
    .SYNOPSIS
        Spoofs hardware sensor information for temperature, voltage, and fan readings
    #>
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [hashtable]$HardwareProfile
    )
    
    Write-ModuleLog "Starting hardware sensors spoofing..." "Info"
    
    try {
        $modifiedCount = 0
        
        # Hardware monitoring paths (safer locations)
        $sensorPaths = @(
            'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}'  # System devices
        )
        
        # Realistic sensor values
        $sensorValues = @{
            "CPUTemperature" = Get-Random -Min 35 -Max 65
            "GPUTemperature" = Get-Random -Min 40 -Max 75
            "SystemTemperature" = Get-Random -Min 30 -Max 50
            "CPUVoltage" = [math]::Round((Get-Random -Min 1.1 -Max 1.4), 2)
            "MemoryVoltage" = [math]::Round((Get-Random -Min 1.2 -Max 1.35), 2)
            "FanSpeed1" = Get-Random -Min 800 -Max 1800
            "FanSpeed2" = Get-Random -Min 600 -Max 1500
            "FanSpeed3" = Get-Random -Min 1000 -Max 2000
        }
        
        foreach ($sensorPath in $sensorPaths) {
            if (Test-Path $sensorPath) {
                $subKeys = Get-ChildItem -Path $sensorPath -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    if ($subKey.Name -match '\d{4}$') {
                        $fullPath = $subKey.PSPath
                        foreach ($sensor in $sensorValues.GetEnumerator()) {
                            Set-RegistryValue -Path $fullPath -Name "Sensor_$($sensor.Key)" -Value $sensor.Value -Type "DWord"
                        }
                        $modifiedCount++
                    }
                }
            }
        }
        
        # Create thermal zone information
        $thermalPath = "HKLM:\SYSTEM\CurrentControlSet\Enum\ACPI\ThermalZone"
        if (-not (Test-Path $thermalPath)) {
            New-Item -Path $thermalPath -Force | Out-Null
        }
        
        for ($zone = 0; $zone -lt 3; $zone++) {
            $zonePath = "$thermalPath\TZ$zone"
            if (-not (Test-Path $zonePath)) {
                New-Item -Path $zonePath -Force | Out-Null
            }
            
            Set-RegistryValue -Path $zonePath -Name "Temperature" -Value (Get-Random -Min 30 -Max 60) -Type "DWord"
            Set-RegistryValue -Path $zonePath -Name "ThermalState" -Value "Normal" -Type "String"
            $modifiedCount++
        }
        
        Write-ModuleLog "Hardware sensors spoofing completed: $modifiedCount entries modified" "Info"
        return @{ Success = $true; Message = "Hardware sensors spoofed with realistic values" }
    }
    catch {
        Write-ModuleLog "Hardware sensors spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

#endregion

# Export functions
Export-ModuleMember -Function @(
    'Invoke-AdvancedCPUSpoofing',
    'Invoke-AdvancedGPUSpoofing',
    'Invoke-AdvancedStorageSpoofing',
    'Invoke-AdvancedAudioSpoofing',
    'Invoke-AdvancedUSBSpoofing',
    'Invoke-SMBIOSSpoofing',
    'Invoke-HardwareSensorsSpoofing'
)

# Module initialization
Write-Verbose "Advanced Spoofing Module loaded successfully"
