# System Properties GPU Information Fix Module
# Specifically targets the registry locations that control graphics card display in system dialogs

Import-Module "$PSScriptRoot\..\..\Core\Logging\Logging.psm1" -Force
Import-Module "$PSScriptRoot\..\..\Core\Registry\RegistryUtils.psm1" -Force

function Invoke-SystemPropertiesGPUFix {
    <#
    .SYNOPSIS
        Fixes graphics card information display in system properties and control panel dialogs
    .DESCRIPTION
        Specifically targets the registry locations that control how graphics card information
        appears in Control Panel > System, System Properties dialog, and similar system information displays
    #>
    [CmdletBinding()]
    param(
        [hashtable]$GPUProfile
    )
    
    Write-ModuleLog "Starting System Properties GPU information fix..." "Info"
    
    try {
        $modifiedCount = 0
        $vramSizeBytes = $GPUProfile.VRAM * 1024 * 1024
        $vramSizeMB = [math]::Round($GPUProfile.VRAM / 1024)
        
        # Critical registry paths for system properties display
        $criticalPaths = @(
            # Main system information registry
            @{
                Path = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"
                Values = @{
                    "GraphicsCard" = $GPUProfile.Model
                    "GraphicsMemory" = $vramSizeMB
                    "DedicatedVideoMemory" = $vramSizeBytes
                    "VideoCardName" = $GPUProfile.Model
                    "VideoCardVendor" = $GPUProfile.Vendor
                }
            },
            
            # Windows NT Current Version (used by Control Panel)
            @{
                Path = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion"
                Values = @{
                    "VideoCardDescription" = $GPUProfile.Model
                    "DisplayAdapter" = $GPUProfile.Model
                    "GraphicsAdapter" = $GPUProfile.Model
                }
            },
            
            # Hardware description system
            @{
                Path = "HKLM:\HARDWARE\DESCRIPTION\System\MultifunctionAdapter\0\DisplayController\0"
                Values = @{
                    "Identifier" = $GPUProfile.Model
                    "Configuration Data" = $GPUProfile.Model
                    "Component Information" = $GPUProfile.Model
                }
            }
        )
        
        foreach ($pathInfo in $criticalPaths) {
            $regPath = $pathInfo.Path
            
            # Ensure the registry path exists
            if (-not (Test-Path $regPath)) {
                try {
                    New-Item -Path $regPath -Force | Out-Null
                    Write-ModuleLog "Created registry path: $regPath" "Debug"
                }
                catch {
                    Write-ModuleLog "Failed to create registry path $regPath : $($_.Exception.Message)" "Warning"
                    continue
                }
            }
            
            # Set all values for this path
            foreach ($valueName in $pathInfo.Values.Keys) {
                try {
                    $valueData = $pathInfo.Values[$valueName]
                    $valueType = if ($valueData -is [int]) { "DWord" } else { "String" }
                    
                    Set-RegistryValue -Path $regPath -Name $valueName -Value $valueData -Type $valueType
                    Write-ModuleLog "Set $regPath\$valueName = $valueData" "Debug"
                    $modifiedCount++
                }
                catch {
                    Write-ModuleLog "Failed to set $regPath\$valueName : $($_.Exception.Message)" "Debug"
                }
            }
        }
        
        # Aggressive VMware SVGA removal from display adapter class
        $displayClassPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
        if (Test-Path $displayClassPath) {
            $subKeys = Get-ChildItem -Path $displayClassPath -ErrorAction SilentlyContinue
            foreach ($subKey in $subKeys) {
                if ($subKey.Name -match '\d{4}$') {
                    $fullPath = $subKey.PSPath
                    try {
                        $properties = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                        
                        # Check for VMware SVGA references
                        $isVMwareGPU = $false
                        if ($properties) {
                            $isVMwareGPU = ($properties.DriverDesc -like "*VMware*") -or 
                                          ($properties.DriverDesc -like "*SVGA*") -or
                                          ($properties.MatchingDeviceId -like "*VEN_15AD*") -or
                                          ($properties.HardwareID -like "*VEN_15AD*")
                        }
                        
                        if ($isVMwareGPU) {
                            # Replace ALL VMware SVGA references with realistic GPU
                            $replacementValues = @{
                                "DriverDesc" = $GPUProfile.Model
                                "DeviceDesc" = $GPUProfile.Model
                                "ProviderName" = $GPUProfile.Vendor
                                "FriendlyName" = $GPUProfile.Model
                                "LocationInformation" = "PCI bus 0, device 2, function 0"
                                "Capabilities" = 0x00000004
                                "ConfigFlags" = 0x00000000
                                "HardwareInformation.AdapterString" = $GPUProfile.Model
                                "HardwareInformation.ChipType" = $GPUProfile.CodeName
                                "HardwareInformation.MemorySize" = $vramSizeBytes
                                "HardwareInformation.AdapterRAM" = $vramSizeBytes
                                "VideoMemorySize" = $vramSizeBytes
                                "DefaultSettings.BitsPerPel" = 32
                                "DefaultSettings.XResolution" = 1920
                                "DefaultSettings.YResolution" = 1080
                                "DefaultSettings.VRefresh" = 60
                            }
                            
                            foreach ($valueName in $replacementValues.Keys) {
                                try {
                                    $valueData = $replacementValues[$valueName]
                                    $valueType = if ($valueData -is [int]) { "DWord" } else { "String" }
                                    
                                    Set-RegistryValue -Path $fullPath -Name $valueName -Value $valueData -Type $valueType
                                }
                                catch {
                                    # Continue if individual value fails
                                }
                            }
                            
                            # Update PCI hardware IDs
                            $vendorId = if ($GPUProfile.Vendor -like "*NVIDIA*") { "10DE" } else { "1002" }
                            $deviceId = $GPUProfile.DeviceID
                            $newHwId = "PCI\VEN_$vendorId&DEV_$deviceId"
                            
                            try {
                                Set-RegistryValue -Path $fullPath -Name "MatchingDeviceId" -Value $newHwId -Type "String"
                                Set-RegistryValue -Path $fullPath -Name "HardwareID" -Value @($newHwId) -Type "MultiString"
                                Set-RegistryValue -Path $fullPath -Name "CompatibleIDs" -Value @($newHwId) -Type "MultiString"
                            }
                            catch {
                                # Continue if hardware ID update fails
                            }
                            
                            $modifiedCount++
                            Write-ModuleLog "Replaced VMware SVGA device in: $fullPath" "Info"
                        }
                    }
                    catch {
                        Write-ModuleLog "Failed to process display adapter $fullPath : $($_.Exception.Message)" "Debug"
                    }
                }
            }
        }
        
        # Force refresh of system information cache
        try {
            $refreshPaths = @(
                "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Setup\State",
                "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation\ComputerHardwareId"
            )
            
            foreach ($refreshPath in $refreshPaths) {
                if (Test-Path $refreshPath) {
                    # Touch the registry to force refresh
                    $currentTime = [int][double]::Parse((Get-Date -UFormat %s))
                    Set-RegistryValue -Path $refreshPath -Name "LastRefresh" -Value $currentTime -Type "DWord"
                }
            }
        }
        catch {
            Write-ModuleLog "Failed to refresh system information cache: $($_.Exception.Message)" "Debug"
        }
        
        Write-ModuleLog "System Properties GPU fix completed: $modifiedCount modifications" "Info"
        return @{ 
            Success = $true
            ModifiedCount = $modifiedCount
            Message = "System Properties GPU information updated to $($GPUProfile.Model) with ${vramSizeMB}MB VRAM"
        }
    }
    catch {
        Write-ModuleLog "System Properties GPU fix failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Export functions
Export-ModuleMember -Function @(
    'Invoke-SystemPropertiesGPUFix'
)

# Module initialization
Write-Verbose "System Properties GPU Fix Module loaded successfully"
