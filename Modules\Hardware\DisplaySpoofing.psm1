# Display Adapter Spoofing Module
#
# This module spoofs display adapter information to avoid VM detection
# Enhanced with proven techniques from Anti-VMDetection.ps1

# Import required modules
Import-Module "$PSScriptRoot\..\Core\Logging\Logging.psm1" -Force -ErrorAction SilentlyContinue
Import-Module "$PSScriptRoot\..\Registry\RegistryOperations\RegistryOperations.psm1" -Force -ErrorAction SilentlyContinue

function Invoke-DisplaySpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )

    Write-ModuleLog "Starting comprehensive display adapter spoofing..." "Info"

    try {
        $gpuSpecs = $Config.HardwareSpecs.GPU
        $spoofedDevices = 0
        
        # Take ownership of registry keys function
        function Set-RegistryKeyOwnership {
            param([string]$RegistryPath)
            try {
                $key = [Microsoft.Win32.Registry]::LocalMachine.OpenSubKey($RegistryPath.Replace("HKLM:\", ""), [Microsoft.Win32.RegistryKeyPermissionCheck]::ReadWriteSubTree, [System.Security.AccessControl.RegistryRights]::ChangePermissions)
                $acl = $key.GetAccessControl()
                $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
                $acl.SetOwner([System.Security.Principal.NTAccount]$currentUser)
                $key.SetAccessControl($acl)
                $key.Close()
                return $true
            } catch {
                return $false
            }
        }

        # Enhanced GPU spoofing with multiple registry paths
        $gpuRegistryPaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}",
            "HKLM:\SYSTEM\CurrentControlSet\Control\Video"
        )
        
        foreach ($basePath in $gpuRegistryPaths) {
            if (Test-Path $basePath) {
                Set-RegistryKeyOwnership -RegistryPath $basePath | Out-Null
                
                try {
                    Get-ChildItem $basePath -ErrorAction Stop | ForEach-Object {
                        $subkeyPath = $_.PSPath
                        Set-RegistryKeyOwnership -RegistryPath $subkeyPath | Out-Null
                        
                        try {
                            $properties = Get-ItemProperty $subkeyPath -ErrorAction SilentlyContinue
                            if ($properties.DriverDesc -match "VMware|SVGA" -or $properties.MatchingDeviceId -match "VEN_15AD") {
                                # Comprehensive NVIDIA RTX 4070 spoofing
                                Set-ItemProperty -Path $subkeyPath -Name "DriverDesc" -Value $gpuSpecs.Model -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "ProviderName" -Value $gpuSpecs.Vendor -Force -ErrorAction SilentlyContinue
                                
                                # NVIDIA-specific driver information
                                Set-ItemProperty -Path $subkeyPath -Name "DriverVersion" -Value $gpuSpecs.DriverVersion -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "DriverDate" -Value $gpuSpecs.DriverDate -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "DriverDateData" -Value ([byte[]](0x00, 0x48, 0x78, 0x19, 0x01, 0xDA, 0x07, 0x00)) -Force -ErrorAction SilentlyContinue
                                
                                # Hardware specifications
                                Set-ItemProperty -Path $subkeyPath -Name "HardwareInformation.MemorySize" -Value $gpuSpecs.MemorySize -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "HardwareInformation.ChipType" -Value $gpuSpecs.Model -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "HardwareInformation.DacType" -Value "Internal" -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "HardwareInformation.AdapterString" -Value $gpuSpecs.Model -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "HardwareInformation.BiosString" -Value "Version 94.04.3A.80.00" -Force -ErrorAction SilentlyContinue
                                
                                # NVIDIA registry keys
                                Set-ItemProperty -Path $subkeyPath -Name "Device Description" -Value $gpuSpecs.Model -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "InfSection" -Value "nv_disp" -Force -ErrorAction SilentlyContinue
                                
                                # Display capabilities
                                Set-ItemProperty -Path $subkeyPath -Name "DefaultSettings.BitsPerPel" -Value 32 -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "DefaultSettings.XResolution" -Value 1920 -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "DefaultSettings.YResolution" -Value 1080 -Force -ErrorAction SilentlyContinue
                                Set-ItemProperty -Path $subkeyPath -Name "DefaultSettings.VRefresh" -Value 60 -Force -ErrorAction SilentlyContinue
                                
                                Write-ModuleLog "Enhanced VMware GPU -> $($gpuSpecs.Model) in $subkeyPath" "Info"
                                $spoofedDevices++
                            }
                        }
                        catch {
                            Write-ModuleLog "Failed to modify GPU registry subkey: $subkeyPath - $_" "Debug"
                        }
                    }
                }
                catch {
                    Write-ModuleLog "Failed to access GPU registry path: $basePath - $_" "Debug"
                }
            }
        }
        
        # Spoof video controller information in Device Manager enumeration
        $videoEnumPath = "HKLM:\SYSTEM\CurrentControlSet\Enum\PCI"
        if (Test-Path $videoEnumPath) {
            Get-ChildItem $videoEnumPath | Where-Object { $_.Name -match "VEN_15AD&DEV_0405" } | ForEach-Object {
                # Convert PSPath to clean registry path
                $pciVideoPath = $_.Name -replace '^.*?\\HKEY_LOCAL_MACHINE', 'HKLM:'
                Set-RegistryKeyOwnership -RegistryPath $pciVideoPath | Out-Null
                
                Get-ChildItem $pciVideoPath | ForEach-Object {
                    # Convert PSPath to clean registry path
                    $instancePath = $_.Name -replace '^.*?\\HKEY_LOCAL_MACHINE', 'HKLM:'
                    Set-RegistryKeyOwnership -RegistryPath $instancePath | Out-Null
                    
                    # Replace with NVIDIA RTX 4070 PCI information
                    Set-ItemProperty -Path $instancePath -Name "DeviceDesc" -Value "@oem13.inf,%nvidia_dev.2783.01%;$($gpuSpecs.Model)" -Force -ErrorAction SilentlyContinue
                    Set-ItemProperty -Path $instancePath -Name "FriendlyName" -Value $gpuSpecs.Model -Force -ErrorAction SilentlyContinue
                    Set-ItemProperty -Path $instancePath -Name "Mfg" -Value "@oem13.inf,%nvidia%;NVIDIA" -Force -ErrorAction SilentlyContinue
                    
                    # Update Hardware IDs to NVIDIA
                    $nvidiaHardwareIDs = @(
                        "PCI\VEN_10DE&DEV_2783&SUBSYS_40981462&REV_A1",
                        "PCI\VEN_10DE&DEV_2783&SUBSYS_40981462",
                        "PCI\VEN_10DE&DEV_2783&CC_030000",
                        "PCI\VEN_10DE&DEV_2783&CC_0300"
                    )
                    Set-ItemProperty -Path $instancePath -Name "HardwareID" -Value $nvidiaHardwareIDs -Force -ErrorAction SilentlyContinue
                    
                    # Compatible IDs
                    $nvidiaCompatibleIDs = @(
                        "PCI\VEN_10DE&DEV_2783&REV_A1",
                        "PCI\VEN_10DE&DEV_2783",
                        "PCI\VEN_10DE&CC_030000",
                        "PCI\VEN_10DE&CC_0300",
                        "PCI\VEN_10DE",
                        "PCI\CC_030000",
                        "PCI\CC_0300"
                    )
                    Set-ItemProperty -Path $instancePath -Name "CompatibleIDs" -Value $nvidiaCompatibleIDs -Force -ErrorAction SilentlyContinue
                    
                    # Device properties
                    Set-ItemProperty -Path $instancePath -Name "Class" -Value "Display" -Force -ErrorAction SilentlyContinue
                    Set-ItemProperty -Path $instancePath -Name "ClassGUID" -Value "{4d36e968-e325-11ce-bfc1-08002be10318}" -Force -ErrorAction SilentlyContinue
                    
                    Write-ModuleLog "Spoofed VMware SVGA PCI device -> $($gpuSpecs.Model)" "Info"
                    $spoofedDevices++
                }
            }
        }
        
        # Modify video controller services
        $videoServicePath = "HKLM:\SYSTEM\CurrentControlSet\Services\vm3dmp"
        if (Test-Path $videoServicePath) {
            Set-RegistryKeyOwnership -RegistryPath $videoServicePath | Out-Null
            
            # Change VMware 3D service to NVIDIA service
            Set-ItemProperty -Path $videoServicePath -Name "DisplayName" -Value "NVIDIA Display Driver Service" -Force -ErrorAction SilentlyContinue
            Set-ItemProperty -Path $videoServicePath -Name "Description" -Value "NVIDIA Display Driver Service" -Force -ErrorAction SilentlyContinue
            
            Write-ModuleLog "Spoofed VMware 3D service -> NVIDIA Display Service" "Info"
            $spoofedDevices++
        }
        
        # Remove VMware SVGA from DirectX registry
        $directxPath = "HKLM:\SOFTWARE\Microsoft\DirectDraw\MostRecentApplication"
        if (Test-Path $directxPath) {
            $dxProperties = Get-ItemProperty $directxPath -ErrorAction SilentlyContinue
            if ($dxProperties.Name -match "VMware|SVGA") {
                Set-ItemProperty -Path $directxPath -Name "Name" -Value $gpuSpecs.Model -Force -ErrorAction SilentlyContinue
                Write-ModuleLog "Updated DirectX application reference" "Info"
            }
        }

        
        # Also check System Devices for VMware VMCI Bus Device
        $systemClassPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e97d-e325-11ce-bfc1-08002be10318}"
        if (Test-Path $systemClassPath) {
            Get-ChildItem -Path $systemClassPath -ErrorAction SilentlyContinue | ForEach-Object {
                # Convert PSPath to clean registry path
                $keyPath = $_.Name -replace '^.*?\\HKEY_LOCAL_MACHINE', 'HKLM:'
                $props = Get-ItemProperty -Path $keyPath -ErrorAction SilentlyContinue
                
                if ($props.DriverDesc -like "*VMware*" -or $props.DriverDesc -like "*VMCI*") {
                    Write-ModuleLog "Found VMware system device at: $keyPath" "Info"
                    
                    try {
                        # Take ownership if needed
                        Set-RegistryKeyOwnership -RegistryPath $keyPath | Out-Null
                        
                        Set-ItemProperty -Path $keyPath -Name "DriverDesc" -Value "Intel(R) Management Engine Interface" -Force
                        Set-ItemProperty -Path $keyPath -Name "DeviceDesc" -Value "Intel(R) Management Engine Interface" -Force -ErrorAction SilentlyContinue
                        
                        # Also update the FriendlyName if it exists
                        if ($props.PSObject.Properties['FriendlyName']) {
                            Set-ItemProperty -Path $keyPath -Name "FriendlyName" -Value "Intel(R) Management Engine Interface" -Force -ErrorAction SilentlyContinue
                        }
                        
                        Write-ModuleLog "Changed VMware system device to Intel ME Interface" "Info"
                        $spoofedDevices++
                    } catch {
                        Write-ModuleLog "Failed to update system device: $_" "Warning"
                    }
                }
            }
        }

        if ($spoofedDevices -gt 0) {
            Write-ModuleLog "Comprehensive GPU spoofing completed successfully. Modified $spoofedDevices device(s)" "Info"
            return @{ Success = $true; Message = "Display adapter spoofed to $($gpuSpecs.Model) ($spoofedDevices devices)" }
        } else {
            Write-ModuleLog "No VMware display adapters found to spoof" "Warning"
            return @{ Success = $true; Message = "No VMware display adapters found to spoof" }
        }
    }
    catch {
        Write-ModuleLog "Display adapter spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

Export-ModuleMember -Function 'Invoke-DisplaySpoofing'
