# CD-ROM Spoofing Module
#
# This module spoofs the CD-ROM drive information to avoid VM detection.

# Import required modules
Import-Module "$PSScriptRoot\..\Core\Logging\Logging.psm1" -Force -ErrorAction SilentlyContinue
Import-Module "$PSScriptRoot\..\Registry\RegistryOperations\RegistryOperations.psm1" -Force -ErrorAction SilentlyContinue

function Invoke-CDROMSpoofing {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )

    Write-ModuleLog "Starting CD-ROM spoofing..." "Info"

    try {
        $cdromSpecs = $Config.HardwareSpecs.CDROM
        $spoofedDevices = 0

        # Method 1: Direct registry path spoofing (proven to work)
        Write-ModuleLog "Searching for CD-ROM devices using direct registry paths..." "Debug"
        
        # Look for NECVMWar VMware SATA CD devices
        $vmwareCDPaths = @(
            'HKLM:\SYSTEM\CurrentControlSet\Enum\SCSI\CdRom&Ven_NECVMWar&Prod_VMware_SATA_CD01',
            'HKLM:\SYSTEM\CurrentControlSet\Enum\IDE\CdRom*'
        )
        
        foreach ($basePath in $vmwareCDPaths) {
            if (Test-Path $basePath) {
                $subKeys = Get-ChildItem -Path $basePath -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    $fullPath = $subKey.PSPath
                    $props = Get-ItemProperty -Path $fullPath -ErrorAction SilentlyContinue
                    
                    if ($props.FriendlyName -like "*VMware*" -or $props.FriendlyName -like "*NECVMWar*") {
                        Write-ModuleLog "Found VMware CD-ROM at: $fullPath" "Info"
                        Write-ModuleLog "Current FriendlyName: $($props.FriendlyName)" "Debug"
                        
                        # Update FriendlyName
                        $newDescription = "$($cdromSpecs.Vendor) $($cdromSpecs.Model)"
                        Set-ItemProperty -Path $fullPath -Name "FriendlyName" -Value $newDescription -Force
                        Write-ModuleLog "Changed FriendlyName to: $newDescription" "Info"
                        $spoofedDevices++
                    }
                }
            }
        }

        # Method 2: Comprehensive search for any CD/DVD devices
        $enumPaths = @(
            'HKLM:\SYSTEM\CurrentControlSet\Enum\SCSI',
            'HKLM:\SYSTEM\CurrentControlSet\Enum\IDE'
        )

        foreach ($enumPath in $enumPaths) {
            if (Test-Path $enumPath) {
                # Get all subkeys that might contain CD-ROM devices
                $deviceKeys = Get-ChildItem -Path $enumPath -Recurse -ErrorAction SilentlyContinue | 
                    Where-Object { 
                        $_.PSChildName -notmatch "Properties|Device Parameters|Volatile"
                    }

                foreach ($deviceKey in $deviceKeys) {
                    try {
                        $keyPath = $deviceKey.PSPath
                        $props = Get-ItemProperty -Path $keyPath -ErrorAction SilentlyContinue
                        
                        # Check if this is a CD-ROM device
                        if ($props.Class -eq "CDROM" -or 
                            $props.FriendlyName -like "*CD*" -or 
                            $props.FriendlyName -like "*DVD*" -or
                            $props.DeviceDesc -like "*CD*" -or 
                            $props.DeviceDesc -like "*DVD*") {
                            
                            # Only update if it contains VMware references
                            if ($props.FriendlyName -like "*VMware*" -or 
                                $props.FriendlyName -like "*NECVMWar*" -or
                                $props.DeviceDesc -like "*VMware*") {
                                
                                Write-ModuleLog "Found CD-ROM device to spoof at: $keyPath" "Debug"
                                
                                # Update device descriptions
                                $newDescription = "$($cdromSpecs.Vendor) $($cdromSpecs.Model)"
                                
                                if ($props.FriendlyName) {
                                    Set-ItemProperty -Path $keyPath -Name "FriendlyName" -Value $newDescription -Force
                                    Write-ModuleLog "Updated FriendlyName" "Debug"
                                }
                                
                                if ($props.DeviceDesc) {
                                    Set-ItemProperty -Path $keyPath -Name "DeviceDesc" -Value $newDescription -Force
                                    Write-ModuleLog "Updated DeviceDesc" "Debug"
                                }
                                
                                $spoofedDevices++
                            }
                        }
                    }
                    catch {
                        # Skip devices we can't access
                        continue
                    }
                }
            }
        }

        # Also check device class registry
        $cdromClassKey = 'HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e965-e325-11ce-bfc1-08002be10318}'
        if (Test-Path $cdromClassKey) {
            $subKeys = Get-ChildItem -Path $cdromClassKey -ErrorAction SilentlyContinue
            foreach ($subKey in $subKeys) {
                if ($subKey.Name -match '\d{4}$') {
                    $driverDesc = $subKey.GetValue("DriverDesc")
                    if ($driverDesc -like "*VMware*" -or $driverDesc -like "*NECVMWar*") {
                        $keyPath = $subKey.PSPath
                        Set-RegistryValue -Path $keyPath -Name "DriverDesc" -Value "$($cdromSpecs.Vendor) $($cdromSpecs.Model)" -Type "String"
                        $spoofedDevices++
                    }
                }
            }
        }

        if ($spoofedDevices -gt 0) {
            Write-ModuleLog "CD-ROM spoofing completed successfully. Modified $spoofedDevices device(s)" "Info"
            return @{ Success = $true; Message = "CD-ROM spoofed to $($cdromSpecs.Vendor) $($cdromSpecs.Model) ($spoofedDevices devices)" }
        } else {
            Write-ModuleLog "No CD-ROM devices found to spoof" "Warning"
            return @{ Success = $true; Message = "No CD-ROM devices found to spoof" }
        }
    }
    catch {
        Write-ModuleLog "CD-ROM spoofing failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

Export-ModuleMember -Function 'Invoke-CDROMSpoofing'

