# Logging Module - Simplified Version
function Initialize-Logging {
    param([hashtable]$Config)
    $global:LogConfig = $Config
}

function Global:Write-ModuleLog {
    param([string]$Message, [string]$Level = "Info")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    $color = switch($Level) {
        "Error" { "Red" }
        "Warning" { "Yellow" }
        "Debug" { "Gray" }
        default { "White" }
    }
    Write-Host $logMessage -ForegroundColor $color
}

function Global:Write-Progress-Log {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

function Global:Get-LogFile {
    return "$env:TEMP\AntiVM-$(Get-Date -Format 'yyyyMMdd').log"
}

# Export functions to global scope for better availability
Export-ModuleMember -Function @('Initialize-Logging', 'Write-ModuleLog', 'Write-Progress-Log', 'Get-LogFile')

# Also create global aliases to ensure availability
Set-Alias -Name 'Write-Log' -Value 'Write-ModuleLog' -Scope Global -Force
New-Variable -Name 'LoggingInitialized' -Value $true -Scope Global -Force
