#Requires -Version 5.1

<#
.SYNOPSIS
    Hardware CPU Spoofing Module for Anti-VM Detection Toolkit

.DESCRIPTION
    Implements comprehensive CPU characteristics spoofing including processor information,
    cache specifications, feature sets, and microcode details to evade VM detection.

.NOTES
    Module: Hardware.CPU
    Author: Cybersecurity Research Team
    Version: 2.0-Modular
    Dependencies: Core.Logging, Core.Utilities, Registry.RegistryPrivileges
#>

# Module-level variables
$script:ModuleConfig = $null
$script:HardwareConfig = $null
$script:ModifiedComponents = @()

#region Public Functions

function Initialize-CPUSpoofing {
    <#
    .SYNOPSIS
        Initializes the CPU spoofing module with configuration
    
    .PARAMETER Config
        Configuration object
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Config
    )
    
    $script:ModuleConfig = $Config.modules.hardware.cpu
    $script:HardwareConfig = $Config.hardware.cpu
    Write-Log "CPU Spoofing module initialized" -Level Debug
}

function Invoke-CPUSpoofing {
    <#
    .SYNOPSIS
        Main CPU spoofing function that coordinates all CPU spoofing operations
    #>
    [CmdletBinding()]
    param()
    
    if (-not $script:ModuleConfig.enabled) {
        Write-Log "CPU spoofing module disabled in configuration" -Level Info
        return
    }
    
    Write-Log "Implementing comprehensive CPU characteristics spoofing..." -Level Info
    
    try {
        # Spoof processor information for all CPU cores
        if ($script:ModuleConfig.spoofProcessorInfo) {
            Invoke-ProcessorInfoSpoofing
        }
        
        # Modify CPU feature set to remove hypervisor indicators
        if ($script:ModuleConfig.modifyFeatureSet) {
            Invoke-CPUFeatureSetModification
        }
        
        # Update cache information
        if ($script:ModuleConfig.updateCacheInfo) {
            Invoke-CPUCacheInfoUpdate
        }
        
        # Generate realistic CPU serial
        if ($script:ModuleConfig.generateRealisticSerial) {
            Invoke-CPUSerialGeneration
        }
        
        # Update system-wide CPU information
        Invoke-SystemWideCPUUpdate

        # Apply WMI modifications
        if ($script:ModuleConfig.modifyWMI) {
            Invoke-WMIModification
        }

        # Perform system refresh for changes to take effect
        if ($script:ModuleConfig.performSystemRefresh) {
            Invoke-SystemRefresh
        }

        Write-Log "Comprehensive CPU spoofing implemented successfully" -Level Info
        return $script:ModifiedComponents
    }
    catch {
        Write-Log "CPU spoofing failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Get-CPUSpoofingResults {
    <#
    .SYNOPSIS
        Returns the results of CPU spoofing operations
    #>
    [CmdletBinding()]
    param()
    
    return @{
        ModifiedComponents = $script:ModifiedComponents
        HardwareConfig = $script:HardwareConfig
        Success = $script:ModifiedComponents.Count -gt 0
    }
}

#endregion

#region Private Functions

function Invoke-ProcessorInfoSpoofing {
    <#
    .SYNOPSIS
        Spoofs processor information for all CPU cores using proven working method
    #>
    [CmdletBinding()]
    param()

    try {
        Write-Log "Phase 1: Hardware Registry Modification..." -Level Info
        $totalModified = 0

        # Use the proven working approach - modify up to 32 cores directly without ownership changes
        for ($i = 0; $i -lt 32; $i++) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$i"
            if (Test-Path $cpuPath) {
                try {
                    # Use the working approach - direct modification with Force parameter
                    Set-ItemProperty -Path $cpuPath -Name "ProcessorNameString" -Value $script:HardwareConfig.brand -Force
                    Set-ItemProperty -Path $cpuPath -Name "Identifier" -Value "Intel64 Family $($script:HardwareConfig.family) Model $($script:HardwareConfig.model) Stepping $($script:HardwareConfig.stepping)" -Force
                    Set-ItemProperty -Path $cpuPath -Name "VendorIdentifier" -Value $script:HardwareConfig.vendor -Force
                    Set-ItemProperty -Path $cpuPath -Name "~MHz" -Value ([int]$script:HardwareConfig.baseClock) -Type DWord -Force

                    Write-Log "Core $i - Modified successfully" -Level Debug
                    $script:ModifiedComponents += "CPU-Core-$i"
                    $totalModified++
                } catch {
                    Write-Log "Core $i - Failed: $($_.Exception.Message)" -Level Warning
                }
            }
        }

        Write-Log "Hardware registry modification completed: $totalModified cores modified" -Level Info
    }
    catch {
        Write-Log "Processor info spoofing failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Invoke-CPUFeatureSetModification {
    <#
    .SYNOPSIS
        Modifies CPU feature set to remove hypervisor indicators
    #>
    [CmdletBinding()]
    param()
    
    try {
        $cpuCores = 0..($script:HardwareConfig.cores - 1)
        
        foreach ($coreIndex in $cpuCores) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$coreIndex"
            if (Test-Path $cpuPath) {
                Set-RegistryKeyOwnership -RegistryPath $cpuPath | Out-Null
                
                # Remove hypervisor bit from feature set (CPUID leaf 0x1, ECX bit 31)
                Set-ItemProperty -Path $cpuPath -Name "FeatureSet" -Value 0x178BFBFF -Force -ErrorAction SilentlyContinue
                
                # Intel microcode and stepping
                $intelMicrocode = Get-Random -Minimum 0x01000000 -Maximum 0x01FFFFFF
                Set-ItemProperty -Path $cpuPath -Name "Update Revision" -Value $intelMicrocode -Force -ErrorAction SilentlyContinue
                
                Write-Log "Modified CPU feature set for core $coreIndex (removed hypervisor bit)" -Level Debug
            }
        }
        
        $script:ModifiedComponents += "CPU-FeatureSet-All-Cores"
        Write-Log "CPU feature set modification completed" -Level Debug
    }
    catch {
        Write-Log "CPU feature set modification failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Invoke-CPUCacheInfoUpdate {
    <#
    .SYNOPSIS
        Updates CPU cache information to match realistic values
    #>
    [CmdletBinding()]
    param()
    
    try {
        $cpuCores = 0..($script:HardwareConfig.cores - 1)
        
        foreach ($coreIndex in $cpuCores) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$coreIndex"
            if (Test-Path $cpuPath) {
                Set-RegistryKeyOwnership -RegistryPath $cpuPath | Out-Null
                
                # CPU Cache information (L1, L2, L3)
                Set-ItemProperty -Path $cpuPath -Name "Level1InstructionCache" -Value $script:HardwareConfig.cacheL1 -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $cpuPath -Name "Level1DataCache" -Value $script:HardwareConfig.cacheL1 -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $cpuPath -Name "Level2Cache" -Value $script:HardwareConfig.cacheL2 -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $cpuPath -Name "Level3Cache" -Value $script:HardwareConfig.cacheL3 -Force -ErrorAction SilentlyContinue
                
                Write-Log "Updated CPU cache info for core $coreIndex (L1:$($script:HardwareConfig.cacheL1), L2:$($script:HardwareConfig.cacheL2), L3:$($script:HardwareConfig.cacheL3))" -Level Debug
            }
        }
        
        $script:ModifiedComponents += "CPU-Cache-Info"
        Write-Log "CPU cache information update completed" -Level Debug
    }
    catch {
        Write-Log "CPU cache info update failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Invoke-CPUSerialGeneration {
    <#
    .SYNOPSIS
        Generates and applies realistic CPU serial numbers
    #>
    [CmdletBinding()]
    param()
    
    try {
        $cpuCores = 0..($script:HardwareConfig.cores - 1)
        
        foreach ($coreIndex in $cpuCores) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$coreIndex"
            if (Test-Path $cpuPath) {
                Set-RegistryKeyOwnership -RegistryPath $cpuPath | Out-Null
                
                # Generate realistic CPU serial number
                $cpuSerial = New-RealisticCPUSerial
                Set-ItemProperty -Path $cpuPath -Name "ProcessorSerialNumber" -Value $cpuSerial -Force -ErrorAction SilentlyContinue
                
                Write-Log "Generated realistic CPU serial for core $coreIndex : $cpuSerial" -Level Debug
            }
        }
        
        $script:ModifiedComponents += "CPU-Serial-Numbers"
        Write-Log "CPU serial number generation completed" -Level Debug
    }
    catch {
        Write-Log "CPU serial generation failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Invoke-SystemWideCPUUpdate {
    <#
    .SYNOPSIS
        Updates system-wide CPU information using comprehensive proven approach
    #>
    [CmdletBinding()]
    param()

    try {
        Write-Log "Phase 2: Device Manager Modifications..." -Level Info

        # Modify Device Class entries (affects Device Manager display)
        $processorClass = Get-ChildItem "HKLM:\SYSTEM\CurrentControlSet\Control\Class" -ErrorAction SilentlyContinue |
            Where-Object {
                $class = Get-ItemProperty $_.PSPath -ErrorAction SilentlyContinue
                $class.Class -eq "Processor"
            }

        if ($processorClass) {
            $devices = Get-ChildItem $processorClass.PSPath -ErrorAction SilentlyContinue
            foreach ($device in $devices) {
                try {
                    Set-ItemProperty -Path $device.PSPath -Name "FriendlyName" -Value $script:HardwareConfig.brand -Force -ErrorAction SilentlyContinue
                    Set-ItemProperty -Path $device.PSPath -Name "DeviceDesc" -Value $script:HardwareConfig.brand -Force -ErrorAction SilentlyContinue
                    Write-Log "Device class updated for Device Manager" -Level Debug
                    $script:ModifiedComponents += "CPU-DeviceManager-Class"
                } catch {
                    # Skip protected entries
                }
            }
        }

        # Modify ACPI Enum entries (critical for Device Manager visibility)
        $enumPaths = @(
            "HKLM:\SYSTEM\CurrentControlSet\Enum\ACPI",
            "HKLM:\SYSTEM\CurrentControlSet\Enum\Root\ACPI"
        )

        foreach ($enumPath in $enumPaths) {
            if (Test-Path $enumPath) {
                $cpuDevices = Get-ChildItem $enumPath -ErrorAction SilentlyContinue |
                    Where-Object { $_.PSChildName -match "Processor|CPU|GenuineIntel|AuthenticAMD" }

                foreach ($device in $cpuDevices) {
                    $instances = Get-ChildItem $device.PSPath -ErrorAction SilentlyContinue
                    foreach ($instance in $instances) {
                        try {
                            Set-ItemProperty -Path $instance.PSPath -Name "FriendlyName" -Value $script:HardwareConfig.brand -Force -ErrorAction SilentlyContinue
                            Set-ItemProperty -Path $instance.PSPath -Name "DeviceDesc" -Value $script:HardwareConfig.brand -Force -ErrorAction SilentlyContinue
                            $script:ModifiedComponents += "CPU-ACPI-Enum"
                        } catch {
                            # Skip protected entries
                        }
                    }
                }
            }
        }

        Write-Log "Phase 3: System Properties Modifications..." -Level Info

        # Environment variables (affects System Properties)
        try {
            $envPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Environment"
            $identifier = "Intel64 Family $($script:HardwareConfig.family) Model $($script:HardwareConfig.model) Stepping $($script:HardwareConfig.stepping)"
            Set-ItemProperty -Path $envPath -Name "PROCESSOR_IDENTIFIER" -Value $identifier -Force
            [Environment]::SetEnvironmentVariable("PROCESSOR_IDENTIFIER", $identifier, "Machine")
            Write-Log "Environment variables updated for System Properties" -Level Debug
            $script:ModifiedComponents += "CPU-Environment-Variables"
        } catch {
            Write-Log "Environment variables update failed" -Level Warning
        }

        # System Information registry
        try {
            $sysInfoPath = "HKLM:\SYSTEM\CurrentControlSet\Control\SystemInformation"
            if (!(Test-Path $sysInfoPath)) {
                New-Item -Path $sysInfoPath -Force | Out-Null
            }

            New-ItemProperty -Path $sysInfoPath -Name "ProcessorNameString" -Value $script:HardwareConfig.brand -PropertyType String -Force | Out-Null
            New-ItemProperty -Path $sysInfoPath -Name "ProcessorSpeed" -Value ([int]$script:HardwareConfig.baseClock) -PropertyType DWord -Force | Out-Null
            Write-Log "System information registry updated" -Level Debug
            $script:ModifiedComponents += "CPU-SystemInfo-Registry"
        } catch {
            Write-Log "System information registry update failed" -Level Warning
        }

        Write-Log "System-wide CPU update completed" -Level Info
    }
    catch {
        Write-Log "System-wide CPU update failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Invoke-WMIModification {
    <#
    .SYNOPSIS
        Attempts to modify WMI data for CPU information
    #>
    [CmdletBinding()]
    param()

    try {
        Write-Log "Phase 4: WMI Modifications..." -Level Info

        # Stop WMI service temporarily
        Stop-Service "WinMgmt" -Force -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 2

        # Create MOF for WMI modification
        $mofContent = @"
#pragma namespace("\\\\.\\root\\cimv2")
instance of __Win32Provider as `$P
{
    Name = "CIMWin32";
};
instance of __InstanceModificationEvent
{
    TargetInstance = instance of Win32_Processor
    {
        Name = "$($script:HardwareConfig.brand)";
        Description = "$($script:HardwareConfig.brand)";
        MaxClockSpeed = $([int]$script:HardwareConfig.baseClock);
    };
};
"@

        $mofFile = "$env:TEMP\CompleteCPU.mof"
        $mofContent | Out-File $mofFile -Encoding ASCII
        mofcomp $mofFile 2>$null
        Remove-Item $mofFile -Force -ErrorAction SilentlyContinue

        # Restart WMI service
        Start-Service "WinMgmt" -ErrorAction SilentlyContinue
        Write-Log "WMI modification attempted" -Level Debug
        $script:ModifiedComponents += "CPU-WMI-Modification"
    } catch {
        Write-Log "WMI modification failed (this is normal): $($_.Exception.Message)" -Level Warning
    }
}

function Invoke-SystemRefresh {
    <#
    .SYNOPSIS
        Performs system refresh operations to make CPU changes visible
    #>
    [CmdletBinding()]
    param()

    try {
        Write-Log "Phase 5: System Refresh..." -Level Info

        # Close Device Manager if open
        Get-Process "mmc" -ErrorAction SilentlyContinue |
            Where-Object {$_.MainWindowTitle -like "*Device Manager*"} |
            Stop-Process -Force -ErrorAction SilentlyContinue

        # Restart critical services
        try {
            Restart-Service "PlugPlay" -Force -ErrorAction SilentlyContinue
            Write-Log "PlugPlay service restarted" -Level Debug
        } catch {
            Write-Log "Failed to restart PlugPlay service" -Level Warning
        }

        try {
            Restart-Service "SystemEventsBroker" -Force -ErrorAction SilentlyContinue
            Write-Log "SystemEventsBroker service restarted" -Level Debug
        } catch {
            Write-Log "Failed to restart SystemEventsBroker service" -Level Warning
        }

        # Force hardware rescan
        try {
            Start-Process "pnputil.exe" -ArgumentList "/scan-devices" -Wait -NoNewWindow -ErrorAction SilentlyContinue
            Write-Log "Hardware rescan completed" -Level Debug
        } catch {
            Write-Log "Hardware rescan failed" -Level Warning
        }

        # Restart explorer for System Properties refresh
        try {
            Stop-Process -Name "explorer" -Force -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
            Start-Process "explorer.exe" -ErrorAction SilentlyContinue
            Write-Log "Explorer restarted for System Properties refresh" -Level Debug
        } catch {
            Write-Log "Explorer restart failed" -Level Warning
        }

        Write-Log "System refresh completed" -Level Info
        $script:ModifiedComponents += "CPU-System-Refresh"
    } catch {
        Write-Log "System refresh failed: $($_.Exception.Message)" -Level Warning
    }
}

#endregion

# Export public functions
Export-ModuleMember -Function @(
    'Initialize-CPUSpoofing',
    'Invoke-CPUSpoofing',
    'Get-CPUSpoofingResults'
)
